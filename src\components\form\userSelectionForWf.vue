<template>
    <div class="w-100">
    <p class="next-action-desc">
        Please choose the users to work on {{ selectedStartingStep.name ?? "the current step" }}
    </p>
    <div class="flex v-center gap-2" ref='userSelectionContainer'>
        <component
        :ref="'multiuserFieldref'"
        :data="{caption: '', required: false, visibility:false}"
        :is="'MULTI_USER_COMPONENT'"
        mode="EDIT"
        :viewOnly="false"
        @customChange="handleUserSelection"
        :showOnlyActiveUsers="true"
        :selectedUsers="selectedUsers"
        />
    </div>
    </div>
</template>
<script>
import multiUserComponent from '@/components/form/elements/multiUserComponent.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'userSelectionForWf',
  components: {
    MULTI_USER_COMPONENT: multiUserComponent
  },
  props: {
    selectedStartingStep: {
      type: Object,
      default: () => ({})
    },
    selectedUsers: {
      type: Array,
      default: () => ([])
    }
  },
  computed: {
    ...mapGetters(['isExternalCollaborator', 'collaboratorId'])
  },
  methods: {
    handleUserSelection (data) {
      this.$emit('handleUserSelection', data)
    },
    styleUserSelectionContainer () {
      const container = this.$refs.userSelectionContainer
      if (container) {
        const select = container?.getElementsByTagName('select')
        select[0].style.padding = ' 0 0.85em'
        select[0].style.height = '25px'
        select[0].style.fontSize = '.8rem'
        const textInput = container.querySelector('input[type="text"]')
        textInput.style.padding = '0 0.85em'
        textInput.style.height = '25px'
        textInput.style.fontSize = '.8rem'
        const badge = container.querySelectorAll('.form-user-bedge') ?? []
        badge.forEach((item) => {
          item.style.fontSize = '.8rem'
        })
      }
    }
  },
  mounted () {
    this.styleUserSelectionContainer()
  }
}

</script>
<style lang="scss" scoped>
.next-action-desc{
font-size: 11px;
color: #666;
}
</style>
