.grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  align-items: center;
}

.elipsis-text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.break-text {
  word-break: break-all;
}

.fw {
  width: 100%;
}

.fh {
  height: 100%;
}
.pointer {
  cursor: pointer;
}

.drawer {
  z-index: 2;
  position: fixed;
  top: 100px;
  bottom: 20px;
  right: 0;
  transform-origin: right;
  background-color: var(--bg-color);
  transition: transform 0.6s linear;
  box-shadow: -2px 2px 4px rgba(0, 0, 0, 0.15);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  overflow-y: auto;
  &.open {
    transform: translateX(0);
  }
  &.close {
    transform: translateX(100%);
  }

}
.overflow-auto{
  overflow: auto;
}
