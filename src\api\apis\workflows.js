import store from '@/store'
import { runMutation, runQuery } from '../graphQl'
import { GetWorkflowByIdQuery, CreateWorkflowStepsQuery, CreateWorkflowTransitionssQuery, DeleteWfStepsAndTransitionsQuery, getWorkflowInstanceStepDataQuery, getWorkflowStepWithUsersQuery, getStepAssigneesQuery } from '../query/workflows'

export const getWorkflowById = (id) => {
  return runQuery(GetWorkflowByIdQuery(), { id }, 'tenant')
}

export const deleteWfStepsAndTransitions = (workflowId) => {
  return runMutation(DeleteWfStepsAndTransitionsQuery(), { workflowId }, 'tenant')
}

export const insertWorkflowSteps = (data) => {
  return runMutation(CreateWorkflowStepsQuery(), { data }, 'tenant')
}

export const insertWorkflowTransitions = (data) => {
  return runMutation(CreateWorkflowTransitionssQuery(), { data }, 'tenant')
}
export const getWorkflowInstanceStepData = (id) => {
  return runMutation(getWorkflowInstanceStepDataQuery(), { id }, 'tenant')
}
export const getWorkflowStepWithUsers = (versionId) => {
  return runMutation(getWorkflowStepWithUsersQuery(), { versionId }, 'tenant')
}
export const getStepAssignees = (instanceId, stepId) => {
  const tokenType = store.getters?.isOnProjectLevel ? 'project' : 'tenant'
  return runQuery(getStepAssigneesQuery(), { instanceId, stepId }, tokenType)
}
