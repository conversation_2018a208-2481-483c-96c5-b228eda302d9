<template>
    <div class="part-id-generation">
      <div class="new-component-box">
        <div class="flex">
      <div class="xxxl weight-500">Tenant Feature Configration</div>
        </div>
    </div>
      <div class="component-cards">
        <div class="tab-headers">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-header', { active: selectedTab === tab.name }]"
        @click="selectedTab = tab.name"
      >
        {{ tab.name }}
      </div>
    </div>
    <div class="create-form--container">
       <div class="fh center" v-if="loading">
          <loading-circle />
        </div>
        <div  v-if="!loading && currentFeatureConfig">
       <div
          class="component-card"
        >
       <div class="component-row">
            <div class="col">{{ selectedTab }} FORM TEMPLATE</div>
              <div class="col">
             <label>{{ selectedTab !== 'DOCUMENT' ? templateName : 'N/A' }}</label>
            </div>
           </div>
           </div>
        <div
          class="component-card"
        >
           <div class="component-row">
            <div class="col">SEQUENCE GENERATOR MATERIAL</div>
              <div class="col">
               <div class=" pointer" v-if="isTenantAdmin">
            <div class="selector" @mouseleave="visible=false, searchkeyword = ''">
                <div class="label" @click="toggleSequence">
                    <span> {{ selectedValue.name || 'Select Material' }} </span>
                </div>
                <div class="arrow"  @click="toggleSequence"></div>
                <div v-if="visible">
                  <div class="form-input">
                    <input placeholder="search sequence name" v-model="searchkeyword"/>
                    <div class="baselinelist">
                    <slot></slot>
                      <div :class="{ current: item.id === selectedValue.id }"  v-for="item in filterdList" :key="item.id" @click="handleSequenceChange(item)">
          <div style="padding-left: 10px; padding-top: 6px">{{ item.name }}</div>
        </div></div>
</div>
                </div>
            </div>
        </div>
        <div v-else>
          <label>{{ selectedValue?.name || 'N/A' }}</label>
          </div>
            </div>

           </div>
           </div>
            <div v-if="selectedTab === 'MATERIAL'" class="component-card">
           <div class="component-row">
            <div class="col">SEQUENCE GENERATOR PRODUCTCODE</div>
              <div class="col" v-if="isTenantAdmin">
               <div class=" pointer">
            <div class="selector" @mouseleave="productCodeview=false, searchkeyword = ''">
                <div class="label" @click="toggleProductCode">
                    <span> {{ productCodeValue.name || 'Select product code' }} </span>
                </div>
                <div class="arrow"  @click="toggleProductCode"></div>
                <div v-if="productCodeview">
                  <div class="form-input" >
                    <input placeholder="search sequence name" v-model="searchkeyword"/>
                    <div class="baselinelist">
                    <slot></slot>
                      <div :class="{ current: item.id === productCodeValue.id }"  v-for="item in filterdList" :key="item.id" @click="handleProductcodeChange(item)">
          <div style="padding-left: 10px; padding-top: 6px">{{ item.name }}</div>
        </div></div>
</div>
                </div>
            </div>
        </div>
            </div>
            <div v-else class="col">
              <label>{{ productCodeValue.name || 'N/A' }}</label>
              </div>

           </div>
           </div>
        </div>
        <div class="pt-9 flex-end">
        <button class="btn mr-3" v-if="isTenantAdmin" :disabled="disable" @click="save">Save</button>
        </div>
        </div>
      </div>
    </div>
  </template>
<script>
import { GetTemplateData, getDetailFormTemplate } from '@/api'
import { GetTenantConfigTypes, updateTenantCofig } from '@/api/apis/tenantConfigFeature'
import { success } from '@/plugins/notification'
import { mapGetters } from 'vuex'
import loadingCircle from '@/components/common/loadingCircle.vue'
export default {
  components: { loadingCircle },
  name: 'config-feature',
  data () {
    return {
      selectedValue: { default: { id: -1, name: 'Select' } },
      productCodeValue: { default: { id: -1, name: 'Select' } },
      selectedTab: 'MATERIAL',
      tenantFeatureList: [],
      sequenceList: null,
      templateName: null,
      loading: false,
      disable: false,
      visible: false,
      tabs: [
        {
          id: 1,
          name: 'MATERIAL'
        },
        {
          id: 2,
          name: 'BOM'
        },
        {
          id: 3,
          name: 'DOCUMENT'
        }
      ],
      productCodeview: false,
      searchkeyword: ''
    }
  },
  watch: {
    selectedTab () {
      this.initializeConfig()
    }
  },
  computed: {
    ...mapGetters(['user', 'getUserById', 'isOnProjectLevel']),
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN'
      )
    },
    currentFeatureConfig () {
      return this.tenantFeatureList[this.selectedTab] || null
    },
    filterdList () {
      return this.sequenceList.filter((item) => {
        if (!this.productCodeview && item.core_feature.name === this.selectedTab) {
          return item.name
            .toLowerCase()
            .includes(this.searchkeyword.toLowerCase())
        } else if (this.productCodeview && item.core_feature.name === 'PRODUCT_CODE') {
          return item.name
            .toLowerCase()
            .includes(this.searchkeyword.toLowerCase())
        }
      })
    }
  },
  mounted () {
    this.initializeConfig()
  },
  methods: {
    toggleSequence () {
      this.visible = !this.visible
    },
    toggleProductCode () {
      this.productCodeview = !this.productCodeview
    },
    handleProductcodeChange (values) {
      this.productCodeValue = values
      this.productCodeview = false
    },
    handleSequenceChange (values) {
      this.selectedValue = values
      this.visible = false
    },
    async initializeConfig () {
      this.loading = true
      try {
        const [templateRes, configRes] = await Promise.all([
          GetTemplateData(),
          GetTenantConfigTypes()
        ])

        this.sequenceList = templateRes.core_sequence_id_template
        this.tenantFeatureList = configRes.tenant_defaults[0].tenant_feature_configuration

        if (this.selectedTab !== 'DOCUMENT') {
          const config = this.currentFeatureConfig
          if (config?.FORM_TEMPLATE_DEFAULT) {
            const detail = await getDetailFormTemplate(config.FORM_TEMPLATE_DEFAULT)
            this.templateName = detail.core_form_templates_by_pk.name

            if (config.SEQUENCE_TEMPLATE_DEFAULT) {
              this.selectedValue = this.sequenceList.find(seq => seq.id === config.SEQUENCE_TEMPLATE_DEFAULT)
            }
            if (this.selectedTab === 'MATERIAL') {
              const productCodeSequenceId = this.tenantFeatureList.PRODUCT_CODE?.SEQUENCE_TEMPLATE_DEFAULT
              if (productCodeSequenceId) {
                this.productCodeValue = this.sequenceList.find(seq => seq.id === productCodeSequenceId)
              }
            }
          }
        }
      } catch (err) {
        console.error('Initialization failed:', err)
      } finally {
        this.loading = false
      }
    },
    save () {
      this.disable = true
      updateTenantCofig(this.selectedTab, this.selectedValue.id, this.productCodeValue.id).then((res) => {
        success(res.message)
        this.disable = false
      })
    }
  }
}
</script>

  <style scoped>
  .new-component-box {
    border-bottom: 2px solid #ddd;
    padding-block: 10px;
  }

  .component-cards {
    margin-top: 30px;
    width: 100%;
  }

  .component-card {
    display: table;
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ccc;
    table-layout: fixed;
    margin-top: 20px;
  }

  .component-row {
    display: table-row;
  }

  .component-header {
    font-weight: bold;
    background-color: #f3f3f3;
  }
  .tabs {
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;
  font-family: sans-serif;
}

.tab-headers {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #ccc;
  border: 1px solid #ccc;
}

.tab-header {
  padding: 12px 80px;
  cursor: pointer;
  /* flex: 1; */
  text-align: center;
  transition: background 0.3s;
}

.tab-header:hover {
  background-color: #e0e0e0;
}

.tab-header.active {
  background-color: #f1f3f5;
  font-weight: 500;
  border-bottom: 2px solid var(--brand-color);
}

  .col {
    display: table-cell;
    padding: 8px 8px 8px 8px;
    border: 1px solid #ccc;
    vertical-align: middle;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  .selector {
        position: relative;
        border-radius: 0.3em;
        .arrow {
            position: absolute;
            right: 10px;
            top: 40%;
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 10px solid #888;
            transform: rotateZ(0deg) translateY(0px);
            transition-duration: 0.3s;
            transition-timing-function: cubic-bezier(.59, 1.39, .37, 1.01);
        }

        .label {
        width: 100%;
        box-sizing: border-box;
        font-size: 14px;
        padding: 4px 6px;
        border: 1px solid var(--brand-color);
        border-radius: 4px;
        background-color: var(--brand-light-color);
        }
    }

    .current {
        background: var(--brand-color);
    }

    .hidden {
        visibility: hidden;
    }

    .visible {
        visibility: visible;
    }
    .form-input{
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    border: 1px solid rgb(179, 179, 179);
    border-radius: 0.3em;
    z-index: 1;
    max-height: 200px;
}

.baselinelist {
    overflow: auto;
    max-height: 153px;
    list-style-type: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
 }
  .options {
    background-color: #ffffff;
  }

  .create-form--container{
    padding: 20px;
    max-width: 700px;
    overflow-y: visible;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    margin-top: 10px;
    border-radius: 6px;
    background: white;
}
  </style>
