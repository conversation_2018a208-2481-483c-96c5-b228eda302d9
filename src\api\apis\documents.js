import { runQuery, runMutation } from '../graphQl'
import * as documentsQuery from '../query/documents'
import http from '../http'
import config from '../../config.js'
import store from '../../store'

export const generateS3SubmittingUrl = (body) => {
  return http.POST(config.serverEndpoint + '/files/upload', body)
}

export const generateS3DownloadingUrl = (body) => {
  return http.POST(config.serverEndpoint + '/files/downloadMulti', body)
}
export const generateS3DownloadingUrlSingle = (body) => {
  return http.POST(config.serverEndpoint + '/files/download', body)
}

export const checkDocExists = (docName, folderId, isOnProjectLevel = false) => {
  return runMutation(documentsQuery.checkDocExistsQuery(), { docName, folderId }, isOnProjectLevel ? 'project' : 'tenant')
}

export const insertDocument = (blobKey, description, docName, docSize, docType, folder, parentId, versionOf, docExt, thumbnailBlobKey = null) => {
  let token = 'tenant'
  const insertObj = {
    blob_key: blobKey,
    description: description,
    doc_name: docName,
    doc_size: docSize,
    doc_type: docType,
    folder: folder,
    parent_id: parentId,
    version_of: versionOf,
    doc_ext: docExt
  }
  if (thumbnailBlobKey) {
    insertObj.thumbnail_blob_key = thumbnailBlobKey
  }
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
    if (store.getters.user.projectLevelRole === 'EDITOR' && store.getters?.isOnProjectLevel) {
      insertObj.folder = undefined
    }
  }
  if (store.getters?.isExternalCollaborator) {
    insertObj.folder = undefined
  }
  if (store.getters.user.tenantLevelRole === 'EDITOR' && !store.getters?.isOnProjectLevel) {
    insertObj.folder = undefined
  }
  return runMutation(documentsQuery.insertDocumentQuery(), { insertObj }, token)
}
// this is update last  checkout version of  document
export const updateLastdocversion = (blobKey, docId) => {
  return runMutation(documentsQuery.updateLastdocVersionQuery(), {
    blob_key: blobKey,
    doc_id: docId
  }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}

export const getAllParentFolders = (explicitToken = null) => {
  let token = 'tenant'
  const conditions = { folder: { _eq: true }, parent_id: { _is_null: true }, version_of: { _is_null: true }, deleted: { _eq: false } }
  if (store.getters?.isOnProjectLevel && explicitToken !== 'tenant') {
    token = 'project'
    conditions.project_id = { _eq: store.getters.currentProject.id }
  }
  return runQuery(documentsQuery.getAllParentFoldersQuery(), { conditions }, token)
}
export const getAllParentFoldersWithToken = (projectId, token) => {
  const conditions = { folder: { _eq: true }, parent_id: { _is_null: true }, version_of: { _is_null: true }, deleted: { _eq: false } }
  conditions.project_id = { _eq: projectId }
  return runQuery(documentsQuery.getAllParentFoldersQuery(), { conditions }, 'current', token)
}

export const DeleteDocumentByVersionId = (id, project = false) => {
  return runMutation(documentsQuery.DeleteDocByVersionIDQuery(), {
    id
  }, project ? 'project' : 'tenant')
}

export const getDocumentsByParentId = (parentId) => {
  let token = 'tenant'
  let skipInheritedField = true
  const conditions = { parent_id: { _eq: parentId }, deleted: { _eq: false }, version_of: { _is_null: true } }
  if (store.getters?.isOnProjectLevel) {
    skipInheritedField = false
    token = 'project'
    conditions.project_id = { _eq: store.getters.currentProject.id }
  }
  return runQuery(documentsQuery.getDocumentsByParentIdQuery(), { conditions, skipInheritedField }, token)
}
export const getDocumentsByParentIdMatNull = (parentId, explicitToken, materialAttachment, showOnlyLocked = true) => {
  let token = 'tenant'
  const conditions = { parent_id: { _eq: parentId }, deleted: { _eq: false }, version_of: { _is_null: true } }
  if (materialAttachment) {
    // conditions.material_id = { _is_null: true }
  }
  if (store.getters?.isOnProjectLevel && explicitToken !== 'tenant') {
    token = 'project'
    conditions.project_id = { _eq: store.getters.currentProject.id }
  }
  // for creating a document in project level, show both locked and checkin documents for inherit and copy, for attachments only need to show locked documents
  if (showOnlyLocked) {
    conditions.state = { _eq: config.DOC_STATE_MAP.LOCK }
  } else {
    conditions.state = { _in: [config.DOC_STATE_MAP.LOCK, config.DOC_STATE_MAP.CHECKIN] }
  }
  return runQuery(documentsQuery.getDocumentsByParentIdQuery2(), { conditions }, token)
}
// this for timeline , there each task might have different token and project id
export const getDocumentsByParentIdMatNullWithToken = (parentId, projectId, token) => {
  const conditions = { parent_id: { _eq: parentId }, deleted: { _eq: false }, version_of: { _is_null: true } }
  conditions.project_id = { _eq: projectId }
  return runQuery(documentsQuery.getDocumentsByParentIdQuery2(), { conditions }, 'current', token)
}

export const updateDocumentName = (id, docName) => {
  let token = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
  }
  return runMutation(documentsQuery.updateDocumentNameQuery(), {
    id: id,
    doc_name: docName
  }, token)
}

export const deleteDocument = (id) => {
  let token = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
  }
  return runMutation(documentsQuery.deleteDocumentQuery(), {
    id: id
  }, token)
}

export const getDocumentById = (id, explicitToken) => {
  let token = 'tenant'
  const conditions = { id: { _eq: id } }
  if (store.getters?.isOnProjectLevel && explicitToken !== 'tenant') {
    token = 'project'
    conditions.project_id = { _eq: store.getters.currentProject.id }
  }
  return runQuery(documentsQuery.getDocumentByIdQuery(), { conditions }, token)
}
export const getDocumentByIdWithToken = (id, projectId, token) => {
  const conditions = { id: { _eq: id } }
  conditions.project_id = { _eq: projectId }
  return runQuery(documentsQuery.getDocumentByIdQuery(), { conditions }, 'current', token)
}

export const getAllCollabDocument = () => {
  const projectId = store.getters.projectIdForCollaborator
  const userId = store.getters?.user?.userId
  const collaboratorId = store.getters?.collaboratorId
  const conditions = { deleted: { _eq: false } }
  conditions.collaborator_documents = {
    target_tenant_user_id: { _eq: userId }, source_tenant_id: { _eq: collaboratorId }
  }
  let skipInheritedField = true
  if (!projectId) {
    conditions.project_id = { _is_null: true }
  } else if (projectId) {
    skipInheritedField = false
    conditions.project_id = { _is_null: false }
    conditions.project_id = { _eq: projectId }
  }
  return runQuery(documentsQuery.getAllCollaboratorDocumentQuery(), { conditions, skipInheritedField }, 'tenant')
}

export const ShareDocument = (data, level) => {
  if (level === 'tenant') {
    return runMutation(documentsQuery.shareDocument(), { data }, 'tenant')
  } else if (level === 'project') {
    return runMutation(documentsQuery.shareDocument(), { data }, 'project')
  }
}

export const removeDocAccess = (id) => {
  return runMutation(documentsQuery.removeDocAccess(), { id }, 'tenant')
}

export const GetUsersWithAccessToShareDocument = (documentId) => {
  return runQuery(documentsQuery.getUsersWithAccessToSharedDocument(), { documentId }, 'tenant')
}

export const getAllRevisionDocuments = (versionOf) => {
  let token = 'tenant'
  let skipInheritedField = true
  const conditions = { _or: [{ id: { _eq: versionOf } }, { version_of: { _eq: versionOf } }], deleted: { _eq: false } }
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
    skipInheritedField = false
  }
  return runQuery(documentsQuery.getAllRevisionDocumentsQuery(), { conditions, skipInheritedField }, token)
}

// export const getRevisionDocsforAttaching = (versionOf) => {
//   return runQuery(documentsQuery.getRevisionDocsforAttachingQuery(), {
//     id: versionOf
//   }, token)
// }

export const setDocumentState = (id, state, checkedOutBy) => {
  let token = 'tenant'
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
  }
  return runMutation(documentsQuery.setDocumentStateQuery(), {
    id: id,
    state: state,
    checked_out_by: checkedOutBy
  }, token)
}

export const CheckDuplicateFolderName = (folderName) => {
  let token = 'tenant'
  const conditions = { doc_name: { _eq: folderName }, deleted: { _eq: false } }
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
    conditions.project_id = { _eq: store.getters.currentProject.id }
  }
  return runQuery(documentsQuery.checkDuplicateFolderName(), { conditions }, token)
}
export const downloadDocument = (url) => {
  return http.GET(url)
}

export const inheritDocument = (docExt, inheritFromDocId, parentId, docName) => {
  const insertObj = {
    doc_ext: docExt,
    inherited_from_doc_id: inheritFromDocId,
    parent_id: parentId,
    doc_name: docName,
    folder: false
  }
  if (store.getters.user.tenantLevelRole === 'EDITOR' && !store.getters?.isOnProjectLevel) {
    insertObj.folder = undefined
  }
  if (store.getters.user.projectLevelRole === 'EDITOR' && store.getters?.isOnProjectLevel) {
    insertObj.folder = undefined
  }
  return runMutation(documentsQuery.inheritDocument(), { insertObj }, 'project')
}

export const updateInheritedDocument = (latestId, docId) => {
  return runMutation(documentsQuery.updateInheritedDocument(), { latestId, docId }, 'project')
}

export const renameDocument = (id, newName) => {
  let token
  if (store.getters?.isOnProjectLevel) {
    token = 'project'
  } else {
    token = 'tenant'
  }
  return runMutation(documentsQuery.renameDocumentMutation(), { id, newName }, token)
}

export const getAllAttachedDocs = () => {
  return runQuery(documentsQuery.getAllAttachedDocs(), {}, 'project')
}

export const SwitchDownload = (id, value, token = 'tenant') => {
  return runMutation(documentsQuery.SwitchDownloadQuery(), { id, value }, token)
}
export const insertDocAnnotations = (objects) => {
  return runMutation(documentsQuery.insertDocAnnotationMutation(), { objects }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getAnnotationDataApi = (docId) => {
  return runMutation(documentsQuery.getAnnotationDataApiQuery(), { docId }, store.getters.collaborator ? 'tenant' : store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const deleteAllAnnotation = (docId) => {
  return runMutation(documentsQuery.deleteAllAnnotationQuery(), { docId }, store.getters.collaborator ? 'tenant' : store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const updateAnnotations = (updateObjects) => {
  return runMutation(documentsQuery.updateAnnotationQuery(), { updateObjects }, store.getters.collaborator ? 'tenant' : store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const deleteAnnoatationElements = (ids) => {
  return runMutation(documentsQuery.deleteAnnoatationElementsQuery(), { ids }, store.getters.collaborator ? 'tenant' : store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const checkFolderHasDocsRevision = (folderId) => {
  return runQuery(documentsQuery.CheckIfFolderHasDocsUnderRevision(), { folderId }, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
