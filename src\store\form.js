import { getFormTypeList, getForm<PERSON>ields, GetUserListQuerywithInvitedStatus, GetAllProductCode, GetAllTenantsListData, getDetailFormTemplate } from '@/api'

export default {
  namespaced: true,
  state: {
    formTypeList: [],
    formFields: [],
    filters: {},
    customFilters: [],
    formTemplateBody: [],
    selectedFormElementToEdit: null,
    preventElementSelection: false,
    formTemplateName: null,
    formTemplateType: null,
    linkedWFTemplate: {},
    linkedFormSequenceId: {},
    searchKeywordStore: '',
    formFieldsTypeMapStore: {},
    customListMap: {},
    setupData: {
      templateName: '',
      formTypeselected: null,
      selectedSequenceType: null,
      selectedWorkflow: null,
      sequenceTypeId: '',
      workflowId: ''
    },

    // Widget data
    companyList: {
      data: [],
      fetched: false,
      loading: false
    },
    usersList: {
      data: [],
      fetched: false,
      loading: false
    },
    productCodeList: {
      data: [],
      fetched: false,
      loading: false
    }
  },
  getters: {
    searchKeywordStore: state => state.searchKeywordStore,
    formFiltersFromStore: state => state.filters,
    formFieldsTypeMapStore: state => state.formFieldsTypeMapStore,
    customFiltersFromStore: state => state.customFilters,
    formTypeList: state => state.formTypeList,
    formFields: state => state.formFields,
    formTemplateBody: state => state.formTemplateBody,
    selectedFormElementToEdit: state => state.selectedFormElementToEdit,
    preventElementSelection: state => state.preventElementSelection,
    // for getting the form template details for updation
    formTemplateName: state => state.formTemplateName,
    formTemplateType: state => state.formTemplateType,
    linkedWFTemplate: state => state.linkedWFTemplate,
    linkedFormSequenceId: state => state.linkedFormSequenceId,
    customListMap: state => state.customListMap,

    // NEW: Setup data getter
    setupData: state => state.setupData,

    // Widget data
    companyList: state => state.companyList,
    usersList: state => state.usersList,
    productCodeList: state => state.productCodeList
  },
  mutations: {
    setFormFieldsTypeMap (state, value) {
      state.formFieldsTypeMapStore = value
    },
    setSearchKeyword (state, keyword) {
      state.searchKeywordStore = keyword
    },
    setFormTemplateName (state, value) {
      state.formTemplateName = value
    },
    setFormTemplateType (state, value) {
      state.formTemplateType = value
    },
    setlinkedWFTemplate (state, value) {
      state.linkedWFTemplate = value
    },
    setLinkedFormSequenceId (state, value) {
      state.linkedFormSequenceId = value
    },
    setPreventElementSelection (state, value) {
      state.preventElementSelection = value
    },
    setFormTypeList (state, formTypeList) {
      state.formTypeList = formTypeList
    },
    setFormFields (state, formFields) {
      state.formFields = formFields
    },
    setFormTemplateBody (state, formTemplateBody) {
      state.formTemplateBody = formTemplateBody
    },
    setSelectedFormElementToEdit (state, selectedFormElementToEdit) {
      state.selectedFormElementToEdit = selectedFormElementToEdit
    },

    // NEW: Setup data mutations
    setSetupData (state, data) {
      state.setupData = { ...state.setupData, ...data }
    },
    resetSetupData (state) {
      state.setupData = {
        templateName: '',
        formTypeselected: null,
        selectedSequenceType: null,
        selectedWorkflow: null,
        sequenceTypeId: '',
        workflowId: ''
      }
    },

    setUserListLoading (state, loading) {
      state.usersList.loading = loading
    },
    setUserListFetched (state, fetched) {
      state.usersList.fetched = fetched
    },
    setUserListData (state, data) {
      state.usersList.data = data
    },
    setProductCodeListLoading (state, loading) {
      state.productCodeList.loading = loading
    },
    setProductCodeListFetched (state, fetched) {
      state.productCodeList.fetched = fetched
    },
    setProductCodeListData (state, data) {
      state.productCodeList.data = data
    },
    setCompanyListLoading (state, loading) {
      state.companyList.loading = loading
    },
    setCompanyListFetched (state, fetched) {
      state.companyList.fetched = fetched
    },
    setCompanyListData (state, data) {
      state.companyList.data = data
    },
    setFormFilters (state, data) {
      state.filters = data.filters
      state.customFilters = data.customFilters
    },
    resetFormFilters (state) {
      state.filters = {}
      state.customFilters = []
    },
    resetSearchKeyword (state) {
      state.searchKeywordStore = ''
    },
    setCustomListMap (state, data) {
      state.customListMap = data
    }
  },
  actions: {
    // form template search
    saveSearchKeyword ({ commit }, keyword) {
      commit('setSearchKeyword', keyword)
    },
    // saves the formFieldsType
    saveFormFieldsTypeMap ({ commit }, map) {
      commit('setFormFieldsTypeMap', map)
    },
    removeSearchKeyword ({ commit }) {
      commit('resetSearchKeyword')
    },
    saveFormFilters ({ commit }, data) {
      commit('setFormFilters', data)
    },
    removeFormFilters ({ commit }) {
      commit('resetFormFilters')
    },

    // NEW: Setup data actions
    updateSetupData ({ commit }, data) {
      commit('setSetupData', data)
    },
    clearSetupData ({ commit }) {
      commit('resetSetupData')
    },

    async getFormTypeList ({ commit }) {
      const data = await getFormTypeList()
      commit('setFormTypeList', data.core_form_types)
    },
    async getFormFields ({ commit }) {
      const data = await getFormFields()
      commit('setFormFields', data.core_form_fields)
    },
    async getFormTemplateDetailsForUpdation ({ commit }, id) {
      const data = await getDetailFormTemplate(id)
      let activeVersion = {}
      // to get the active versions from the array
      for (const item of data.core_form_templates_by_pk.template_versions) {
        if (item.active) {
          activeVersion = item
          break
        }
      }
      // to change the object structure to use in the forms playground for update
      const formTemplateBody = activeVersion?.template_fields?.map((item) => {
        item.key = item.form_field.key
        item.form_field = undefined
        return item
      }) ?? []
      commit('setFormTemplateBody', formTemplateBody)
      commit('setFormTemplateName', data.core_form_templates_by_pk.name)
      commit('setFormTemplateType', data.core_form_templates_by_pk.core_form_type.id)
      commit('setlinkedWFTemplate', { name: data.core_form_templates_by_pk.workflow_template?.name, id: data.core_form_templates_by_pk.workflow_template?.id })
      commit('setLinkedFormSequenceId', { name: data?.core_form_templates_by_pk?.core_sequence_id_template?.name, id: data?.core_form_templates_by_pk?.core_sequence_id_template?.id })
    },
    addElementToFormTemplateBody ({ commit, state }, element) {
      commit('setFormTemplateBody', [...state.formTemplateBody, element])
      if (element.key === 'CONFIGURATION_LIST') {
        commit('setSelectedFormElementToEdit', state.formTemplateBody.length - 1)
        commit('setPreventElementSelection', true)
      }
    },
    changePreventElementSelection ({ commit, state }, value) {
      commit('setPreventElementSelection', value)
    },
    removeElementFromFormTemplateBody ({ commit, state }, element) {
      const newFormTemplateBody = state.formTemplateBody.filter(el => el.id !== element.id)
      commit('setFormTemplateBody', newFormTemplateBody)
    },
    addTemplateElementToTemplateBody ({ commit, state }, element) {
      const newFormTemplateBody = [...element, ...state.formTemplateBody]
      commit('setFormTemplateBody', newFormTemplateBody)
    },
    removeTemplateElementFormTemplateBody ({ commit, state }) {
      const newFormTemplateBody = state.formTemplateBody.filter(el => !el.fixed)
      commit('setFormTemplateBody', newFormTemplateBody)
    },
    resetFormTemplate ({ commit }) {
      commit('setFormTemplateBody', [])
      commit('setSelectedFormElementToEdit', null)
    },
    setSelectedFormElementToEdit ({ commit }, index) {
      commit('setSelectedFormElementToEdit', index)
    },
    moveElementUp ({ commit, state }, index) {
      if (index <= 0 || index >= state.formTemplateBody.length) {
        return
      }

      const newFormTemplateBody = [...state.formTemplateBody]
      const element = newFormTemplateBody.splice(index, 1)[0]
      newFormTemplateBody.splice(index - 1, 0, element)
      let newSelectedFormElementToEdit = state.selectedFormElementToEdit
      if (state.selectedFormElementToEdit === index) {
        newSelectedFormElementToEdit -= 1 // If currently editing the moved element
      } else if (state.selectedFormElementToEdit > index) {
        newSelectedFormElementToEdit -= 1 // Adjust if it's below the moved element
      }
      commit('setSelectedFormElementToEdit', newSelectedFormElementToEdit)
      commit('setFormTemplateBody', newFormTemplateBody)
    },
    moveElementDown ({ commit, state }, index) {
      const newFormTemplateBody = [...state.formTemplateBody]
      const element = newFormTemplateBody.splice(index, 1)[0]
      newFormTemplateBody.splice(index + 1, 0, element)
      const newSelectedFormElementToEdit = state.selectedFormElementToEdit + 1
      commit('setSelectedFormElementToEdit', newSelectedFormElementToEdit)
      commit('setFormTemplateBody', newFormTemplateBody)
    },
    deleteElement ({ commit, state }, index) {
      const newFormTemplateBody = [...state.formTemplateBody]
      if (newFormTemplateBody[index].key === 'CONFIGURATION_LIST') {
        commit('setPreventElementSelection', false)
      }
      newFormTemplateBody.splice(index, 1)
      commit('setFormTemplateBody', newFormTemplateBody)
      commit('setSelectedFormElementToEdit', null)
    },
    updateElement ({ commit, state }, { index, data }) {
      const newFormTemplateBody = [...state.formTemplateBody]
      newFormTemplateBody.splice(index, 1, data)
      commit('setFormTemplateBody', newFormTemplateBody)
    },
    async getUserList ({ commit, state, rootState }) {
      if (state.usersList.fetched) return
      commit('setUserListLoading', true)
      GetUserListQuerywithInvitedStatus(!!rootState.currentProject?.id).then(data => {
        commit('setUserListData', data.core_users)
        commit('setUserListFetched', true)
        commit('setUserListLoading', false)
      }).finally(() => {
        commit('setUserListLoading', false)
        commit('setUserListFetched', true)
      })
    },
    async getProductCodeList ({ commit, state }) {
      if (state.productCodeList.fetched) return
      commit('setProductCodeListLoading', true)
      GetAllProductCode().then(data => {
        commit('setProductCodeListData', data.product_code)
      }).finally(() => {
        commit('setProductCodeListLoading', false)
        commit('setProductCodeListFetched', true)
      })
    },
    async getCompanyList ({ commit, state, rootState }) {
      if (state.companyList.fetched) return
      commit('setCompanyListLoading', true)
      GetAllTenantsListData(rootState.user.userId, rootState.user.tenantId).then(data => {
        commit('setCompanyListData', data.core_tenants)
      }).finally(() => {
        commit('setCompanyListLoading', false)
        commit('setCompanyListFetched', true)
      })
    },
    saveCustomListMap ({ commit }, value) {
      commit('setCustomListMap', value)
    }
  }
}
