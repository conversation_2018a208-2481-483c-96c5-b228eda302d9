<template>

  <section class="layout">
    <div class="navbar bg v-center space-between">
      <img width="140px" src="~@/assets/images/brand-transparent.png" style="cursor: pointer;" alt="" @click="$router.push('/')"/>
      <div class="v-center">
        <div class="notification-container">
          <div class="notification">
            <notification-box v-if="!(isBeaconAdmin || tenantType === 2)" :notifications="notification" />
          </div>
        </div>
        <!-- <select
          v-if="!isBeaconAdmin"
          class="pointer mx-4 tenant-dropdown"
          v-model="selectedTenant"
          @change="tenantAndProjectChange"
        >
          <option
            v-for="tenant in filterCompany"
            :key="tenant.id"
            :value="{id: tenant.id, collaborator: false}"
          >
            {{ tenant.company_name }}
          </option>
          <optgroup label="Parent Tenants" v-if="parentTenantsList.length">
            <option v-for="parentTenant in parentTenantsList" :key="parentTenant.id"
            :value="{id:parentTenant.source_tenant.id, collaborator: true }">
              {{ parentTenant.source_tenant.company_name }}
            </option>
          </optgroup>
          <optgroup label="Child Tenants" v-if="filteredChildTenants.length">
            <option v-for="childTenant in filteredChildTenants" :key="childTenant.id"
            :value="{id:childTenant.target_tenant.id, childTenant: true }" disabled>
                {{ childTenant.target_tenant.company_name }}
            </option>
          </optgroup>
        </select> -->
        <div v-if="!isBeaconAdmin" class="tenant-dropdown">
          <single-select-drop-down
            :options="filteredChildTenants"
            :filterTenant="filterCompany"
            :current="selectedTenantList"
            :tenantDropdown=true
            @selected="tenantAndProjectChange"
          />
        </div>
        <!-- <select
          v-if="!isBeaconAdmin"
          class="pointer mx-4 tenant-dropdown"
          v-model="selectedProject"
          @change="tenantAndProjectChange"
        >
          <option value="0">Master</option>
          <option
            v-for="project in tenantProjectList"
            :key="project.id"
            :value="project.id"
          >
            {{ project.name }}
          </option>
        </select> -->
        <single-select-drop-down
            label="Projects"
            :options="computedProjectList"
            :current="selectedProjectComputed"
            @selected="projectChange"
            v-if="showProjectDropdown && tenantType !== 2 && !isBeaconAdmin"
          />
        <div class="hamburger relative" v-click-outside="closeOnOutsideClick">
          <img
            @click="openUserOptions = true"
            src="~@/assets/images/hameburger.png"
            alt=""
            class="pointer"
          />
          <div class="user-dropdown" v-if="openUserOptions">
            <div class="user-name text-1 s elipsis-text">
              <span class="brand weight-600">Hi,</span>
              {{ user.first_name }}
              {{ user.last_name }}
            </div>
            <router-link class="user-dropdown--option" to="/profile"
              >Profile</router-link
            >
            <div class="user-dropdown--option pointer" to="#" @click="logout"
              >Logout</div
            >
          </div>
        </div>
      </div>
    </div>
  <sidebar v-if="!isBeaconAdmin" />
    <div :class="!isBeaconAdmin ? 'main bg':'main2 bg'">
      <slot />
    </div>
  </section>
</template>

<script>
import NotificationBox from './notifications.vue'
import SingleSelectDropDown from './singleSelectDropDown.vue'
import Sidebar from './sidebar.vue'
import { mapGetters } from 'vuex'
import Config from '@/config.js'
import { previousPath } from '@/router'
import { logout } from '@/api/session'
export default {
  name: 'LayoutComponent',
  components: { Sidebar, NotificationBox, SingleSelectDropDown },
  data () {
    return {
      module: '',
      notification: [],
      openUserOptions: false,
      selectedTenant: null,
      selectedProject: null
    }
  },
  methods: {
    closeOnOutsideClick () {
      this.openUserOptions = false
    },
    projectChange (project) {
      // project has value like {name:gant,id:900}
      localStorage.removeItem('projectId')
      localStorage.removeItem('dtxProjectToken')

      if (project.id || project.id === 0) {
        this.selectedProject = project.id
        this.$store.commit('setProjectIdForCollaborator', null)
      }
      if (this.collaborator && this.selectedProject !== 0) {
        this.$store.commit('setProjectIdForCollaborator', project.id)
        if (window.location.pathname.includes('bom')) {
          this.$router.replace({ path: '/bom/project', query: { projectId: project.id } })
        } else if (window.location.pathname.includes('insights')) {
          this.$router.replace({ path: '/insights/project/personal', query: { projectId: project.id } })
        }
      } else if (this.collaborator && this.selectedProject === 0) {
        if (window.location.pathname.includes('bom')) {
          this.$router.push('/bom/product')
        } else if (window.location.pathname.includes('insights')) {
          this.$router.push('/insights/product/personal')
        }
      } else {
        this.rearrangeRouting(project.id)
        localStorage.setItem(
          Config.localstorageKeys.LAST_OPENED_PROJECT,
          this.selectedProject

        )
      }
    },
    tenantAndProjectChange (projects) {
      if (projects.id || projects.id === 0) {
        this.selectedTenant = projects.id
      }
      if (!projects.collaborator) {
        localStorage.removeItem(Config.localstorageKeys.COLLABORATOR_ID)
        localStorage.setItem(
          Config.localstorageKeys.LAST_OPENED_TENANT,
          projects.id
        )
      } else {
        localStorage.setItem(
          Config.localstorageKeys.COLLABORATOR_ID,
          projects.id
        )
      }
      localStorage.setItem(Config.localstorageKeys.COLLABORATOR, projects.collaborator)
      localStorage.setItem(Config.localstorageKeys.TENANT_TYPE, projects.tenant_type)
      window.location.href = '/'
    },
    logout,
    rearrangeRouting (id) {
      // id (project id ), means it switched to project level
      switch (window.location.pathname.split('/')[1]) {
      case Config.MODULE_ROUTE.FORM: {
        window.location.replace(`/${Config.MODULE_ROUTE.FORM }`)
        break
      }
      case Config.MODULE_ROUTE.PROJECTS: {
        window.location.replace(`/${Config.MODULE_ROUTE.PROJECTS }`)
        break
      }
      case Config.MODULE_ROUTE.DOCUMENTS : {
        window.location.replace(`/${Config.MODULE_ROUTE.DOCUMENTS }`)
        break
      }
      case Config.MODULE_ROUTE.ITEM_MASTER : {
        window.location.replace(`/${Config.MODULE_ROUTE.ITEM_MASTER }/1`)
        break
      }
      case Config.MODULE_ROUTE.BOM : {
        let endPoint = '/project'
        if (id === 0) {
          endPoint = '/product'
        }
        window.location.replace(`/${Config.MODULE_ROUTE.BOM + endPoint }`)
        break
      }
      case Config.MODULE_ROUTE.PROJECT_PLANNER : {
        window.location.replace(`/${Config.MODULE_ROUTE.PROJECT_PLANNER }`)
        break
      }
      case Config.MODULE_ROUTE.TIME_SHEET : {
        window.location.replace(`/${Config.MODULE_ROUTE.TIME_SHEET }/update`)
        break
      }
      case Config.MODULE_ROUTE.FORECAST : {
        window.location.replace(`/${Config.MODULE_ROUTE.FORECAST }/material-forecast`)
        break
      }
      case Config.MODULE_ROUTE.SETTINGS : {
        window.location.replace(`/${Config.MODULE_ROUTE.SETTINGS }`)
        break
      }
      case Config.MODULE_ROUTE.INSIGHTS : {
        let path = window.location.href
        if (id) {
          path = path.replace('product', 'project')
        } else {
          path = path.replace('project', 'product')
        }
        window.location.href = `${path }`
        break
      }

      default:
        window.location.replace('/')
        break
      }
    }
  },
  computed: {
    ...mapGetters([
      'user',
      'openTenantId',
      'openProjectId',
      'appLoaded',
      'isBeaconAdmin',
      'tenantList',
      'parentTenantsList',
      'childTenantsList',
      'tenantProjectList',
      'isOnProjectLevel',
      'collaborator',
      'collaboratorId',
      'tenantType',
      'projectIdForCollaborator'
    ]),
    filteredChildTenants () {
      return this.childTenantsList.filter((tenant) => ((tenant.status === 1 || tenant.status === 4) && tenant.target_tenant.status === 1))
    },
    showProjectDropdown () {
      return (!this.collaborator || this.module === 'bom' || this.module === 'document-view' || this.module === 'project-planner' || this.module === 'form' || this.module === 'insights')
    },
    filterCompany () {
      return this.tenantList.filter((tenant) => tenant.status === 1)
    },
    computedProjectList () {
      const projectList = [...this.tenantProjectList.map((project) => { return ({ id: project.id, name: project.name }) })]
      if (!this.collaborator || this.$route.path.includes('/form') || this.$route.path.includes('/insights') || this.$route.path.includes('/document-view') || this.$route.path.includes('/bom')) {
        projectList.unshift({ id: 0, name: 'Master' })
      }
      return projectList
    },
    selectedProjectComputed () {
      const projectId = localStorage.getItem('projectId')
      const currentProject = this.tenantProjectList.filter((project) => {
        if (this.selectedProject === project.id) {
          return ({ name: project.name, id: project.id })
        } else if (projectId === project.id) {
          return ({ name: project.name, id: project.id })
        }
      })
      if (currentProject.length > 0) {
        return currentProject[0]
      } else { return ({ name: 'Master', id: 0 }) }
    },
    selectedTenantList () {
      const TenantList = [
        ...this.filteredChildTenants.map(item => ({ id: item.target_tenant.id, company_name: item.target_tenant.company_name })),
        ...this.parentTenantsList.map(item => ({ id: item.source_tenant.id, company_name: item.source_tenant.company_name }))
      ]
      const currentTenant = this.tenantList.filter((tenant) => {
        if (this.selectedTenant === tenant.id) {
          return ({ company_name: tenant.company_name, id: tenant.id })
        }
      })
      const currentProject = TenantList.filter((tenant) => {
        if (this.selectedTenant === tenant.id) {
          return ({ company_name: tenant.company_name, id: tenant.id })
        }
      })
      if (currentProject.length > 0) {
        return currentProject[0]
      } else {
        return currentTenant[0]
      }
    },
    openProjectId () {
      return this.$store.getters.openProjectId
    }
  },
  watch: {
    computedProjectList () {
      if (this.$route.path.includes('/project-planner') && !previousPath.includes('/project-planner')) {
        this.projectChange(this.computedProjectList[0])
      }
    },
    openProjectId (newVal) {
      if (!this.collaborator && newVal) {
        this.selectedProject = newVal
      }
    },
    '$route.path' () {
      this.module = this.$route.path.split('/')[1].toLowerCase()
      if (this.collaborator) {
        if (this.$route.path.includes('/project-planner')) {
          this.projectChange(this.computedProjectList[0])
        }
      }
      if (this.$route.path.split('/')[1].toLowerCase() !== previousPath.split('/')[1].toLowerCase()) {
        this.$store.commit('removeProjectIdForCollaborator')
        if (this.collaborator) this.selectedProject = 0
      }
    }
  },
  mounted () {
    this.module = this.$route.path.split('/')[1].toLowerCase()
    if (this.collaborator) {
      if (this.$route.path.includes('/project-planner')) {
        this.projectChange(this.computedProjectList[0])
      }
      this.selectedTenant = this.collaboratorId
    } else {
      this.selectedTenant = this.openTenantId
    }
    if (!this.collaborator) {
      this.selectedProject = this.openProjectId
    } else {
      this.selectedProject = this.projectIdForCollaborator
    }
  }
}
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-wrap: wrap;
  .user-dropdown {
    position: absolute;
    top: 100%;
    right: -5px;
    width: 200px;
    background-color: var(--bg-color);
    z-index: 4;
    border-radius: 12px;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.2));
    padding: 10px;
    &::before {
      content: "";
      display: block;
      position: absolute;
      height: 0;
      width: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 14px solid var(--bg-color);
      top: -14px;
      right: 14px;
    }
    &--option {
      display: block;
      color: var(--brand-color-1);
      background-color: var(--bg-color);
      font-size: 12px;
      padding: 4px 12px;
      margin-top: 6px;
      border-radius: 0px;
      &:hover {
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
      }
    }
  }
  .tenant-dropdown {
    padding-right: 10px;
    padding: 6px 16px;
  }
  .navbar {
    height: 60px;
    width: 100%;
    padding: 10px;
    border-bottom: var(--border);
  }
  .main {
    height: calc(100vh - 60px);
    width: calc(100vw - 80px);
    overflow: auto;
    padding: 12px;
    // background: var(--white);
    background-color: #fffffffd;
  }
  .main2 {
    height: calc(100vh - 60px);
    width: calc(100vw - 0px);
    overflow: auto;
    padding: 12px;
    // background: var(--white);
    background-color: #fffffffd;
  }
  .notification-container {
  position: relative;
}
/* New styles for the notification box */
.notification-box {
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px;
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 500px;
}
}
.hamburger {
    top: 120%;
  }
.notification-count {
  position: absolute;
  top: 4px;
  left: 20px;
  background-color: var(--brand-color);
  color: white;
  font-size: 12px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
