<template>
  <div class="workflow-template fh">
    <div class="workflow-template-bar v-center space-between px-3">
      <h1 class="weight-500 xl">
    Workflow Templates
      </h1>
      <div class="v-center">
        <div class="input-group search m mx-3">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="Search by name"
          />
        </div>
          <button class="btn btn-black" @click="openModal=true">Create New Template</button>
      </div>
    </div>
    <div class="workflow-template-container">
      <div class="fh center" v-if="loading">
        <loading-circle />
      </div>
      <div
        class="workflow-template-no-form fh center"
        v-if="!loading && tempalateData.length === 0"
      >
        No Workflow Templates
      </div>
      <div
        class="fh mt-2"
        style="align-items: flex-start"
        v-if="!loading && tempalateData.length"
      >
        <div class="workflow-template-group fh">
          <div class="workflow-template-group-header workflow-template-group-row">
            <div class="workflow-template-group-header-item" v-overflow-tooltip>
              Name
            </div>
            <div class="workflow-template-group-header-item" v-overflow-tooltip>
              Created By
            </div>
            <div class="workflow-template-group-header-item" v-overflow-tooltip>
              Created At
            </div>
            <div  class="workflow-template-group-header-item" v-overflow-tooltip>
              Type
            </div>
          </div>
          <div
            v-for="item in tempalateData"
            :key="item.id"
          >
          <div class="workflow-template-group-item workflow-template-group-row pointer"  @click="
                      $router.push(
                        `/workflows/${item.type_value.name}/?tid=${item.id}`
                      )">
            <div class="workflow-template-group-item-name" v-overflow-tooltip>
              {{ item.name }}
            </div>
            <div class="workflow-template-group-item-created-by" v-overflow-tooltip>
              {{ getFullName(item.created_by_user) }}
            </div>
            <div class="workflow-template-group-item-created-at" v-overflow-tooltip>
              {{ item.created_on | timeStampToDateTime }}
            </div>
            <div  class="workflow-template-group-item-action" v-overflow-tooltip>
            {{item.type_value.name}}
            </div>
          </div>
          </div>
          <div class="p-2 flex center">
            <pagination
            :length="toalcount"
            :perPage="perPage"
            :pageNumber="currentPageNo"
            @selectPage="pageSelected"
        ></pagination>
          </div>

        </div>
      </div>

    </div>
    <modal
    :open="openModal"
    @close="openModal=false"
    :closeOnOutsideClick="true"
    title="Select Workflow Type">
    <div class="workflow-type-modal">
        <RouterLink to="/workflows/NORMAL">
        <div  class="center workflow-type-modal-item" >
          <img src="~@/assets/images/icons/inherit-icon.svg" alt="" />
          <div>NORMAL</div>
        </div>
    </RouterLink>
    <RouterLink to=/workflows/PARTIAL/ >
        <div class="center workflow-type-modal-item" >
          <img src="~@/assets/images/icons/copy-icon.svg" alt="" />
          <div>PARTIAL</div>
        </div>
    </RouterLink>
        <RouterLink to="/workflows/FULL">
        <div class="center workflow-type-modal-item">
          <img src="~@/assets/images/icons/add-icon.svg" alt="" />
          <div>FULL</div>
        </div>
    </RouterLink>
      </div>
    </modal>
  </div>
</template>
<script>
import { getAllWorkFLowTemplatesWithPagination } from '@/api'
import loadingCircle from '@/components/common/loadingCircle.vue'
import { timeStampToDateTime } from '@/filters/dateFilter.js'
import Modal from '@/components/common/modal.vue'
import Pagination from '@/components/common/pagination2.vue'
export default {
  components: { loadingCircle, Modal, Pagination },
  name: 'work-flow-template',
  filters: {
    timeStampToDateTime
  },
  data: () => ({
    tempalateData: [],
    searchKeyword: '',
    loading: false,
    openModal: false,
    toalcount: 0,
    perPage: 20,
    currentPageNo: 1,
    window: window
  }),
  methods: {
    getTemplateData (pageNo = 1) {
      this.loading = true
      const height = this.window.innerHeight - 180
      this.perPage = Math.floor(height / 45)
      getAllWorkFLowTemplatesWithPagination(this.perPage, pageNo).then((res) => {
        this.tempalateData = res.workflow_templates
        this.toalcount = res.workflow_templates_aggregate.aggregate.count
        this.currentPageNo = pageNo
        this.loading = false
      }).catch((err) => {
        console.log(err)
        this.loading = false
      })
    },
    getFullName (user) {
      if (user) { return `${user.first_name} ${user.last_name}` }
      return ''
    },
    pageSelected (pageNo) {
      this.$router.push(`/settings/workflows/${pageNo}`)
    }

  },
  computed: {
    showAction () {
      return true
    }
  },
  mounted () {
    this.currentPageNo = parseInt(this.$route?.params?.pageNo)
    this.getTemplateData(this.currentPageNo)
  },
  watch: {
    '$route.params.pageNo' (newNo, oldNo) {
      if (newNo !== oldNo) {
        this.getTemplateData(parseInt(newNo))
      }
    }
  }
}

</script>
<style lang="scss" scoped>
.workflow {
    &-type{
    &-modal{
        width: 640px;
    padding: 60px 20px;
    margin: -10px;
    background-color: var(--bg-color);
    background: rgba(var(--brand-rgb), 0.3);
    display: flex;
    justify-content: space-around;
    &-item  {
      width: 160px;
      height: 160px;
      font-size: 16px;
      background: var(--bg-color);
      border-radius: 8px;
      flex-direction: column;
      color: black;
      row-gap: 10px;
      &:hover {
        box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
    }
}
&-template {
  &-bar {
    height: 60px;
    margin: -12px 0px -12px -12px;
    margin-bottom: 0;
    background: var(--bg-color);
    border-bottom: var(--border);
  }
  &-container {
    height: calc(100% - 60px);
  }
  &-no-form {
    font-size: 20px;
    color: var(--text-color);
  }
  &-group {
    overflow: auto;
    position: relative;
    & > div {
      & > div {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        & > div {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      }
    }
    &-header {
      background-color: var(--brand-color);
      position: sticky;
      top: 0;
      &-item {
        padding: 4px 10px;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color);
      }
    }
    &-row {
      display: grid;
      grid-template-columns: 2fr 3fr 1fr 1fr 80px;
    }
    &-item {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      & > div {
        padding: 4px 10px;
        font-size: 14px;
        color: var(--text-color);
      }
    }
  }

}
}
</style>
