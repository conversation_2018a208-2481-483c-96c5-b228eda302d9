export const envConfig = {
  local: {
    GRAPHQL_ENDPOINT: 'https://beacon-dev.hasura.app/v1/graphql',
    SERVER_ENDPOINT: 'https://dev-be.beacon-dtx.com',
    GRAPHQL_WS_ENDPOINT: 'wss://beacon-dev.hasura.app/v1/graphql'

    // GRAPHQL_ENDPOINT: 'http://localhost:8080/v1/graphql',
    // GRAPHQL_WS_ENDPOINT: 'ws://localhost:8080/v1/graphql',
    // SERVER_ENDPOINT: 'http://localhost:3000'

    // GRAPHQL_ENDPOINT: 'https://beacon-stg.hasura.app/v1/graphql',
    // GRAPHQL_WS_ENDPOINT: 'wss://beacon-stg.hasura.app/v1/graphql',
    // SERVER_ENDPOINT: 'https://stg-be.beacon-dtx.com'

  },
  dev: {
    GRAPHQL_ENDPOINT: 'https://beacon-dev.hasura.app/v1/graphql',
    GRAPHQL_WS_ENDPOINT: 'wss://beacon-dev.hasura.app/v1/graphql',
    SERVER_ENDPOINT: 'https://dev-be.beacon-dtx.com'
  },
  stg: {
    GRAPHQL_ENDPOINT: 'https://beacon-stg.hasura.app/v1/graphql',
    GRAPHQL_WS_ENDPOINT: 'wss://beacon-stg.hasura.app/v1/graphql',
    SERVER_ENDPOINT: 'https://stg-be.beacon-dtx.com'
  },
  prod: {
    GRAPHQL_ENDPOINT: 'https://beacon-stg.hasura.app/v1/graphql',
    GRAPHQL_WS_ENDPOINT: 'wss://beacon-stg.hasura.app/v1/graphql',
    SERVER_ENDPOINT: 'https://stg-be.beacon-dtx.com'
  }
}
