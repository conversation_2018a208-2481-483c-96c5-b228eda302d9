<template>
  <div v-if="visible" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add Custom Field</h3>
        <button class="close-btn" @click="close()">×</button>
      </div>

      <div class="field-group">
        <label>Field Name</label>
        <input
          v-model="fieldName"
          :class="{ 'error-input': errors.name }"
          placeholder="Enter field name"
        />
        <p v-if="errors.name" class="error-msg">{{ errors.name }}</p>
      </div>

      <div class="field-group">
        <label>Field Type</label>
        <div class="custom-dropdown" :class="{ 'error-input': errors.type }">
          <div class="dropdown-trigger" @click="toggleDropdown" :class="{ 'active': isDropdownOpen }">
            <div class="selected-option">
              <img
                v-if="selectedField"
                :src="iconMap[selectedField.key]"
                alt=""
                class="option-icon"
              />
              <span v-if="selectedField">{{ selectedField.caption }}</span>
              <span v-else class="placeholder">Select field type</span>
            </div>
            <svg class="dropdown-arrow" :class="{ 'rotated': isDropdownOpen }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
              <path stroke="#6b7280" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m6 8 4 4 4-4"/>
            </svg>
          </div>

          <div v-if="isDropdownOpen" class="dropdown-options">
            <div
              v-for="field in fields"
              :key="field.id"
              class="dropdown-option"
              @click="selectField(field)"
            >
              <img :src="iconMap[field.key]" alt="" class="option-icon" />
              <span>{{ field.caption }}</span>
            </div>
          </div>
        </div>
        <p v-if="errors.type" class="error-msg">{{ errors.type }}</p>
      </div>

      <div class="actions">
        <button class="btn btn-cancel" @click="close()">Cancel</button>
        <button
          class="btn btn-add"
          :class="{ 'btn-add-active': fieldName.trim() && selectedField }"
          @click="handleAdd"
        >
          Add Field
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { iconMap } from '@/utils/formFieldIcons'
// import { alert } from '@/plugins/notification'

export default {
  props: {
    fields: {
      type: Array,
      required: true
    },
    customFields: {
      type: Array,
      default: () => []
    },
    visible: Boolean
  },
  data () {
    return {
      iconMap,
      fieldName: '',
      selectedField: null,
      isDropdownOpen: false,
      errors: {
        name: '',
        type: ''
      }
    }
  },
  methods: {
    close () {
      this.fieldName = ''
      this.selectedField = null
      this.errors = {}
      this.$emit('close')
    },
    toggleDropdown () {
      this.isDropdownOpen = !this.isDropdownOpen
    },
    selectField (field) {
      this.selectedField = field
      this.isDropdownOpen = false
      // Clear error when field is selected
      if (this.errors.type) {
        this.errors.type = ''
      }
    },
    handleAdd () {
      this.errors = { name: '', type: '' }

      // Validation
      console.log(this.customFields, 'these are custom fields')
      if (!this.fieldName.trim()) {
        this.errors.name = 'Field Name is required.'
      } else if (this.customFields.some(f => f.caption.toLowerCase() === this.fieldName.trim().toLowerCase())) {
        this.errors.name = 'Field Name already exists.'
      }

      if (!this.selectedField) {
        this.errors.type = 'Field type is required.'
      } else if (this.customFields.some(f => f.key === this.selectedField.key)) {
        this.errors.type = 'Field type already added.'
      }

      // Stop if errors exist
      if (this.errors.name || this.errors.type) return

      // Emit valid field
      this.$emit('add-field', {
        name: this.fieldName.trim(),
        key: this.selectedField.key,
        caption: this.selectedField.caption,
        required: false,
        visible: false,
        isCustom: true
      })

      this.fieldName = ''
      this.selectedField = null
      this.$emit('close')
    }
  },
  mounted () {
    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.isDropdownOpen = false
      }
    })
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleClickOutside)
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 12px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.field-group {
  margin-bottom: 16px;
}

.field-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.field-group input {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f9fafb;
  transition: border-color 0.2s, background-color 0.2s;
  box-sizing: border-box;
}

.field-group input:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: white;
}

.field-group input::placeholder {
  color: #9ca3af;
}

/* Custom Dropdown Styles */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-trigger {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f9fafb;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 40px;
}

.dropdown-trigger:hover,
.dropdown-trigger.active {
  border-color: #3b82f6;
  background-color: white;
}

.selected-option {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.selected-option .placeholder {
  color: #9ca3af;
}

.option-icon {
  width: 11px;
  height: 11px;
  opacity: 0.8;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
  transition: transform 0.2s;
  flex-shrink: 0;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dropdown-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  cursor: pointer;
  transition: background-color 0.15s;
  font-size: 14px;
  color: #374151;
}

.dropdown-option:hover {
  background-color: #f3f4f6;
}

.dropdown-option:active {
  background-color: #e5e7eb;
}

.dropdown-option:last-child {
  border-radius: 0 0 8px 8px;
}

.custom-dropdown.error-input .dropdown-trigger {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
}

.error-input {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
}

.error-msg {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 0;
}

.actions {
  display: flex;
  gap: 10px;
  margin-top: 24px;
}

.btn {
  flex: 1;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.btn-cancel:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.btn-add {
  background-color: #9ca3af;
  color: white;
}

.btn-add-active {
  background-color: #1f2937;
}

.btn-add-active:hover {
  background-color: #111827;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
</style>
