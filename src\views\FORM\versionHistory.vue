<template>
  <div
    v-if="isSelectedTab === config.FORM_TAB_STATUS.REVISION_HISTORY"
    class="create-form fh"
  >
    <!-- <div class="fh center" v-if="loading">
        <loading-circle />
      </div> -->
      <div class="fh">
      <div>
        <div class="create-form--container">
          <div>
            <div
              class="grid-item"
              v-for="(item, index) in versionList"
              :key="index"
              :class="{ selected: selectedItemId === item.id }"
            >
              <div
                class="grid-3 w-100"
                @click="!viewOnly && fetchingVersionForm(item)"
              >
                <div class="item-date grid-2">
                  <span> {{ formatDate(item.created_on) }} </span>
                </div>
                <div class="item-version">
                  <span>Version: {{ getVersionCount(item) }} </span>
                  <div>{{ item.created_by_user.first_name }}</div>
                </div>
                <div class="item-time">
                  ⏰ Time: {{ formatTime(item?.updated_on) || " -- " }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
    <div class="history center" v-else-if="loadHistory">
<loading-circle />
    </div>
  <div v-else class="history">
    <div class="history-header">
      <div class="history-header-toggle">
        <div class="history-header-toggle-box">
          <button class="history-header-toggle-btn" @click="openTab('forms')">
            Forms
          </button>
          <button class="history-header-toggle-btn" @click="openTab('wf')">
            Workflow
          </button>
        </div>
        <div
          class="history-header-toggle-selection"
          ref="backgroundShade"
        ></div>
      </div>
    </div>
    <div class="h-100 overflow-auto">
      <div v-for="(dateArray, index) in (tab==='forms'? formHistory : wfHistory)" class="history-body" :key="tab==='forms'? 'forms'+ index : 'wf'+index ">
        <div class="history-body-dateBox">
          <div class="history-body-date">   <img
            src="~@/assets/images/icons/calenderGrey.svg"
            width="12px"
            alt=""
            /> {{dateArray[0].timestamp | timeStampToDate }}</div>
            <hr class="history-body-date-line" />
          </div>
          <div class="history-body-elementsBox">
            <history-element  v-for="(item, index) in dateArray" :key="index" :data="item" />
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import { getFormRevision, getHistory, getWfHistory } from '@/api'
import config from '@/config'
import LoadingCircle from '../../components/common/loadingCircle.vue'
import HistoryElement from './historyElement.vue'
export default {
  components: {
    LoadingCircle,
    HistoryElement
  },
  props: {
    rootFormId: { type: String },
    formId: { type: String },
    instanceId: { type: String },
    viewOnly: { type: Boolean, defalut: false },
    selectedTab: { type: Number }
  },
  name: 'createForm',
  data () {
    return {
      currentDate: new Date().toLocaleDateString('en-CA'),
      templateId: '',
      versionList: null,
      selectedItemId: this.formId,
      isSelectedTab: null,
      config: config,
      loadHistory: false,
      tab: 'forms',
      formHistory: {},
      wfHistory: {}
    }
  },
  filters: {
    timeStampToDate (timestamp) {
      return new Date(timestamp).toLocaleDateString('en-US', {
        weekday: 'long', // Sunday
        month: 'short', // Jul
        day: 'numeric' // 20
      })
    }
  },
  computed: {},
  watch: {
    selectedTab () {
      this.isSelectedTab = this.selectedTab
      this.getHistoryList()
    }
  },
  methods: {
    getHistoryList () {
      this.loadHistory = true
      if (this.isSelectedTab === config.FORM_TAB_STATUS.HISTORY) {
        getHistory(this.formId).then((res) => {
          this.loadHistory = false
          this.formHistory = {}
          for (const item of res.message) {
            const date = new Date(item.timestamp)
            const dateStr = date.toISOString().split('T')[0]
            switch (item.state) {
            case 'UPDATED':
              item.elementColor = config.elementColor.purple
              break
            case 'INSERTED':
              item.elementColor = config.elementColor.green
              break
            case 'DELETED':
              item.elementColor = config.elementColor.red
              break
            default:
              item.elementColor = config.elementColor.blue
              break
            }

            if (!this.formHistory[dateStr]) {
              this.formHistory[dateStr] = []
            }
            this.formHistory[dateStr].push(item)
          }
        })
      }
    },
    getWorkflowHistory () {
      this.loadHistory = true
      if (this.isSelectedTab === config.FORM_TAB_STATUS.HISTORY) {
        getWfHistory(this.instanceId).then((res) => {
          this.loadHistory = false
          this.wfHistory = {}
          for (const item of res.message.data) {
            const date = new Date(item.timestamp)
            const dateStr = date.toISOString().split('T')[0]

            switch (item.state) {
            case 'TRANSITIONED':
              item.elementColor = config.elementColor.purple
              break
            case 'STARTED':
              item.elementColor = config.elementColor.green
              break
            case 'CLOSED':
              item.elementColor = config.elementColor.orange
              break
            case 'ASSIGNED':
              item.elementColor = config.elementColor.blue
              break
            default:
              item.elementColor = config.elementColor.blue
              break
            }

            if (!this.wfHistory[dateStr]) {
              this.wfHistory[dateStr] = []
            }
            this.wfHistory[dateStr].push(item)
          }
        })
      }
    },
    fetchingVersionForm (item) {
      this.selectedItemId = item.id
      this.$emit('selectedVersion', item)
    },
    formatDate (datetime) {
      const date = new Date(datetime)
      const yy = String(date.getFullYear()).slice(2)
      const mm = String(date.getMonth() + 1).padStart(2, '0')
      const dd = String(date.getDate()).padStart(2, '0')
      return `${yy}-${mm}-${dd}`
    },
    formatTime (datetime) {
      if (datetime) {
        const date = new Date(datetime)
        let hours = date.getHours()
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const ampm = hours >= 12 ? 'PM' : 'AM'
        hours = hours % 12 || 12
        return `${String(hours).padStart(2, '0')}:${minutes} ${ampm}`
      }
    },
    getVersionCount (form) {
      const rootId = form.root_parent_id || form.id

      const relatedForms = this.versionList.filter(
        (f) => f.root_parent_id === rootId || f.id === rootId
      )

      const index = relatedForms.findIndex((f) => f.id === form.id)
      return index !== -1 ? relatedForms.length - index : '-'
    },
    getRevisionForm () {
      const updatedFormId = this.rootFormId ? this.rootFormId : this.formId
      if (updatedFormId) {
        getFormRevision(updatedFormId).then((res) => {
          this.versionList = res.core_forms
        })
      }
    },
    openTab (type) {
      if (type === 'forms') {
        this.$refs.backgroundShade.style.left = '0px'
      } else {
        this.$refs.backgroundShade.style.left = '100px'
      }
      this.tab = type
    }
  },
  mounted () {
    this.isSelectedTab = this.selectedTab
    this.getRevisionForm()
    this.getHistoryList()
    this.getWorkflowHistory()
  }
}
</script>

<style lang="scss" scoped>
$selection-width: 100px;
$selection-height: 28px;
$greyShade-color: #b8b7b7;
.history {
--element-unique-color: 238, 159, 214;
    font-size: 1rem;
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100vh;
    overflow: auto;
  &-header {
    padding-inline: 0.5em;
    display: flex;
    width:100%;
    justify-content: center;
    background-color:inherit;
    position: sticky;
    top: 0;
    z-index: 2;

    &-toggle {
      background-color:rgb($greyShade-color, .3);
      position: relative;
      height: calc($selection-height + 2px);
      width: calc($selection-width*2);
      border-radius: 4px;
      &-box {
        position: absolute;
        z-index: 1;
        border: 0.4px solid rgb(219, 214, 214);
        height: calc($selection-height + 2px);
          width: calc($selection-width*2);
        border-radius: 4px;
        display: flex;
        background: transparent;
        font-size: 0.85em;
      }
      &-btn {
        all: unset;
        width: $selection-width;
        height: $selection-height;
        text-align: center;
        cursor: pointer;
        background: transparent;
      }
      &-selection {
        position: absolute;
        width: calc($selection-width + 2px);
        background-color: white;
        left: 0;
        top: 0;
        bottom: 0;
        height: calc($selection-height + 2px);
        z-index: 0;
        border-radius: 4px;
        transition: all 0.3s ease;
      }
    }
    &-text {
      font-size: 0.85em;
      color: rgb(164, 162, 162);
      height: $selection-height;
      display: flex;
      align-items: center;
    }
  }
  &-body {
    margin-top: 1.5em;
    padding:5px;
    // overflow: auto;
    // height: 100%;
    &-dateBox {
      display: flex;
      gap: 10px;
      align-items: center;
      font-size: 0.8em;
    }
    &-date {
      display: flex;
      align-items:center;
      gap: 5px;
      color: #b5b4b4;
    }
    &-date-line {
      flex-grow: 1;
      border: none;
      height: 0.5px;
      background-color: #cccccc81;
    }
    &-elementsBox {
    padding: 5px;
    overflow:auto;
    }
  }
}
.timestamp-container {
  background: var(--bg-color);
  color: #f9fafb;
  padding: 1rem;
  max-width: 900px;
  margin: auto;
  font-family: "Segoe UI", sans-serif;
  border-radius: 10px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  overflow: auto;
  height: -webkit-fill-available;
  height: -moz-available;
  height: fill-available;
}

.timestamp-row {
  display: flex;
  position: relative;
  align-items: flex-start;
}

.timestamp-left {
  width: 167px;
  text-align: right;
  padding-right: 1rem;
  font-size: 0.9rem;
}

.title {
  font-weight: 600;
  color: black;
  margin-bottom: 0.2rem;
}

.subtitle {
  color: black;
  margin-bottom: 0.2rem;
}

.timestamp-center {
  // position: relative;
  // width: 40px;
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  width: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dot {
  width: 14px;
  height: 14px;
  background-color: var(--brand-color);
  border-radius: 50%;
  z-index: 2;
  // margin-top: 2px;
}
.vertical-line {
  width: 1.6px;
  height: 56px; /* Set a fixed height */
  margin-top: 4px; /* Space below the dot */
  background-image: repeating-linear-gradient(
    to bottom,
    #4b5563,
    #4b5563 4px,
    transparent 4px,
    transparent 8px
  );
}
.timestamp-right {
  flex: 1;
  padding-left: 1rem;
}

.detail-line {
  font-size: 14px;
  margin-bottom: 0.3rem;
  color: black;
}

.note {
  font-style: italic;
}

.button-group {
  margin-top: 0.5rem;
  display: flex;
  gap: 10px;
}

.btn {
  padding: 0.4rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  border: none;
}

.primary {
  background-color: #2563eb;
  color: white;
}

.secondary {
  background-color: #1f2937;
  color: #9ca3af;
}
.create-form {
  overflow-y: auto;
  min-height: 91%;
  height: calc(96% - 60px);
  border-radius: 6px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
  background: rgba(255, 196, 103, 0.05);
  &--container {
    padding: 12px;
    max-width: 700px;
    overflow-y: auto;
    background: white;
  }
  &--elements {
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 4px;
  }
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(200px, 1fr)
  ); /* responsive columns */
  gap: 16px;
}

.grid-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  background-color: #f9f9f9;
  font-family: sans-serif;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.grid-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
.grid-item.selected {
  border: 2px solid var(--brand-color);
}
.item-date,
.item-version,
.item-time {
  margin-bottom: 6px;
  font-size: 14px;
}
.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  align-items: center;
  & .label {
  }
  & .value {
  }
}
</style>
