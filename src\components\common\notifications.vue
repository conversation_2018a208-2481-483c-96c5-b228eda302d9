<template>
    <div v-click-outside="endNotifications">
      <div class="notification-container">
        <svg @click="showNotification = !showNotification" style="cursor: pointer;" width="30" height="30" viewBox="0 0 24 24" role="presentation"><path d="M6.485 17.669a2 2 0 002.829 0l-2.829-2.83a2 2 0 000 2.83zm4.897-12.191l-.725.725c-.782.782-2.21 1.813-3.206 2.311l-3.017 1.509c-.495.248-.584.774-.187 1.171l8.556 8.556c.398.396.922.313 1.171-.188l1.51-3.016c.494-.988 1.526-2.42 2.311-3.206l.725-.726a5.048 5.048 0 00.64-6.356 1.01 1.01 0 10-1.354-1.494c-.023.025-.046.049-.066.075a5.043 5.043 0 00-2.788-.84 5.036 5.036 0 00-3.57 1.478z" fill="currentColor" fill-rule="evenodd"></path></svg>
        <!-- Circle span to display notification count -->
        <span class="notification-count">
          {{notificationCount}}
        </span>
      </div>
      <div class="notification-box" v-if="showNotification" ref="notificationContainer">
        <div class="notification-navbar">
          <span class="pl-3">Notifications</span>
          <!-- <img
          src="~@/assets/images/icons/more-icon.svg"
          class="file-card-action--btn"
          alt=""
        /> -->
        </div>
        <div class="box">
          <div class="center" v-if="loading">
            <loading-circle/>
          </div>
          <div v-else>
        <div v-for="(notification, index) in notificationsToShow" :key="notification.id" class="notification-item">
          <div class="notification-content pointer" :class="{'highlighted': shouldHighlight(notification)}" @click="markNotificationAsRead(notification)">
            <!-- <router-link :to="notification.link"> -->
              <div class="notification-message" @click.stop="clickNotification(notification)">
                {{ notification.message }}
              </div>
            <!-- </router-link> -->
            <div class="notification-time">
              <span :class="{'unread-dot': shouldHighlight(notification)}"></span>
              {{ notification.time }}
            </div>
          </div>
          <div class="notification-separator"></div>
          <Loader v-if="index === notificationsToShow.length - 1 && !reachedEndOfNotifications && loading" />
        </div>
        </div>
        </div>
      </div>
    </div>
  </template>

<script>
import { LiveNotifications, ChangeReadStatus } from '../../api/apis/notifications'
import { logout, projectExchangeToken } from '@/api/session'
import loadingCircle from '../../components/common/loadingCircle.vue'
import { Duration } from '../../utils/date'
import { mapGetters } from 'vuex'
import config from '@/config'
import jwtDecode from 'jwt-decode'
import { alert } from '@/plugins/notification'
export default {
  components: { loadingCircle },
  props: {
    notifications: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      loading: true,
      showNotification: false,
      notificationCount: '0',
      page: 0, // current page number
      notificationsPerPage: 50, // number of notifications per page
      allNotifications: [],
      highlightedNotificationId: null,
      reachedEndOfNotifications: false
    }
  },
  computed: {
    ...mapGetters([
      'collaborator',
      'isExternalCollaborator',
      'user'
    ]),
    notificationsToShow () {
      const startIndex = this.page * this.notificationsPerPage
      const endIndex = startIndex + this.notificationsPerPage
      return this.allNotifications.slice(startIndex, endIndex)
    }
  },
  mounted () {
    this.subscribeToNotifications()
  },
  watch: {
    isExternalCollaborator () {
      this.subscribeToNotifications()
    }
  },
  methods: {
    endNotifications () {
      this.showNotification = false
    },
    clickNotification (notification) {
      this.markNotificationAsRead(notification)
      this.showNotification = false
      if (notification.projectId) {
        localStorage.setItem('projectId', notification.projectId)
        projectExchangeToken((exchangedProjectId) => {
          this.$store.dispatch('getCurrentProject', () => {
          // Handle what happens after the token is exchanged (e.g., navigate to the project page)
            this.$router.push(notification.link)
          })
        }, notification.projectId)
      } else this.$router.push(notification.link)
    },
    notificationss () {
      this.showNotification = !this.showNotification
    },
    subscribeToNotifications () {
      LiveNotifications(this.handleNotifications, this.page, this.notificationsPerPage, this.user)
    },
    shouldHighlight (notification) {
      return !notification.readStatus
    },
    markNotificationAsRead (notification) {
      if (!notification.readStatus) {
        notification.readStatus = true // Mark the clicked notification as read
        ChangeReadStatus(notification.id)
      }
    },
    handleNotifications (data, error) {
      this.loading = true
      if (error) {
        this.allNotifications = [{ id: 0, message: 'Something went wrong! Refresh again' }]
        this.loading = false
        // alert('Something went wrong, Try refreshing again.')
        // this is not required
      } else if (!data.length) {
        this.allNotifications = [{ id: 0, message: 'There are no notifications right now!' }]
        this.loading = false
      } else {
        this.notificationCount = data.filter(notification => !notification.read_status).length + '+'
        const transformedNotifications = []
        for (const notification of data) {
          const { id, sent_on: sentOn, project_id: projectId, read_status: readStatus, core_material_master: coreMaterialMaster, core_bom: coreBom, sent_by_user: sentByUser, action, product_code: productCode, metadata: metaData, core_project: project, workflow_instance_id: workflowInstanceId } = notification
          const bomName = coreBom ? coreBom?.name : ''
          const bomId = coreBom ? coreBom?.id : ''
          const bomVersionId = coreBom ? coreBom.bom_versions[0]?.id : ''
          const userName = sentByUser?.first_name || ''
          const productName = productCode?.product_code || ''
          const productId = coreBom?.product_code || ''
          const materialName = coreMaterialMaster?.material_name || ''
          const materialId = coreMaterialMaster?.custom_material_id || ''
          // const taskId = taskData?.id || ''
          const time = Duration(sentOn)
          const metadata = JSON.parse(metaData)
          const previousStepName = metadata?.previous_step_name
          const currentStepName = metadata?.current_step_name
          const taskName = metadata?.task_name || ''
          const templateName = metadata?.template_name || ''
          const documentName = metadata ? metadata?.doc_name : ''
          const parentDocId = metadata ? metadata?.parent_doc_id : ''
          const workflowType = metadata ? metadata?.workflow_type : ''
          // const documentId = metadata ? metadata?.doc_id : ''
          // console.log(documentId, 'doc')
          // const formId = metadata?.form_id
          // const taskId = metadata?.task_id
          const templateId = metadata?.template_id || 0
          const projectID = metadata?.project_id || projectId
          let notificationMessage = ''
          let notificationLink = ''
          switch (action) {
          case 'USER_GROUP_INVITE':
            notificationMessage = `You are added to a user group named ${metadata?.group_name}`
            notificationLink = `/settings/user-groups/detail/${metadata?.group_id}`
            break
          case 'WORKFLOW_PROGRESSION':
            notificationMessage = `${userName} has transitioned form from ${previousStepName} to ${currentStepName}`
            notificationLink = `/workflows/${workflowType}/?wfinstance=${workflowInstanceId}`
            break
          case 'DOCUMENT_VIEW':
            notificationMessage = `Document ${documentName} is viewed by the user ${userName}`
            break
          case 'DOCUMENT_DOWNLOAD':
            notificationMessage = `Document ${documentName} is downloaded by the user ${userName}`
            break
          case 'BOM_CREATE':
            notificationMessage = `A new BOM ${bomName} has been created by ${userName}.`
            if (!projectId) {
              notificationLink = `/bom/product/${productId}/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            } else {
              notificationLink = `/bom/project/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            }
            break

          case 'BOM_CHECKIN':
            notificationMessage = `BOM ${bomName} has been checked in by ${userName}.`
            if (!projectId) {
              notificationLink = `/bom/product/${productId}/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            } else {
              notificationLink = `/bom/project/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            }
            break

          case 'BOM_CHECKOUT':
            notificationMessage = `BOM ${bomName} has been checked out by ${userName}.`
            if (!projectId) {
              notificationLink = `/bom/product/${productId}/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            } else {
              notificationLink = `/bom/project/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            }
            break

          case 'BOM_OBSOLETE':
            notificationMessage = `The BOM ${bomName} has been made obselete by ${userName}.`
            if (!projectId) {
              notificationLink = `/bom/product/${productId}/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            } else {
              notificationLink = `/bom/project/copy-bom/${bomId}?bomVersionId=${bomVersionId}`
            }
            break

          case 'DOCUMENT_CREATE':
            notificationMessage = `The Document ${documentName} has been created by ${userName}.`
            notificationLink = `/document-view/${parentDocId}?selectedDocId=${notification.document_id}`
            break

          case 'DOCUMENT_CHECKIN':
            notificationMessage = `The Document ${documentName} has been Checked in by ${userName}.`
            notificationLink = `/document-view/${parentDocId}?selectedDocId=${notification.document_id}`
            break

          case 'DOCUMENT_CHECKOUT':
            notificationMessage = `The Document ${bomName} has been Checked out by ${userName}.`
            notificationLink = `/document-view/${parentDocId}?selectedDocId=${notification.document_id}`
            break

          case 'PRODUCT_CODE_CREATE':
            notificationMessage = `A new product ${productName} has been created by ${userName}.`
            notificationLink = `/bom/product/${productId}`
            break

          case 'MATERIAL_CREATE':
            notificationMessage = `A new Material ${materialName} has been created by ${userName}.`
            notificationLink = `/materialmaster/1?selectedMaterialId=${materialId}`
            break

          case 'MATERIAL_OBSOLETE':
            notificationMessage = `The Material ${materialName} has been made obselete by ${userName}.`
            notificationLink = `/materialmaster/1?selectedMaterialId=${materialId}`
            break

          case 'FORM_CREATE':
            notificationMessage = `A new Form has been created by ${userName} in template ${templateName}.`
            notificationLink = `/form/viewform/${templateId}/${templateName}/${notification.form_id}`
            break

          case 'FORM_UPDATE':
            notificationMessage = `The Form from template ${templateName} has been updated by ${userName}.`
            notificationLink = `/form/viewform/${templateId}/${templateName}/${notification.form_id}`
            break

          case 'TASK_PROGRESS_UPDATE':
            notificationMessage = `Task ${taskName} progress has been updated`
            notificationLink = `/project-planner?view=board&selectedTaskId=${notification.task_id}`
            break

          case 'TASK_ASSIGN':
            notificationMessage = `The Task ${taskName} has been assigned to you by ${userName}.`
            notificationLink = `/project-planner?view=board&selectedTaskId=${notification.task_id}`
            break

          case 'BOM_SHARE':
            notificationMessage = `The Bom has been shared to you by ${userName}.`
            notificationLink = `/project-planner?view=board&selectedTaskId=${notification.task_id}`
            break

          case 'DOCUMENT_SHARE':
            notificationMessage = `The Document ${documentName} has been shared to you by ${userName}.`
            notificationLink = `/document-view/${parentDocId}?selectedDocId=${notification.document_id}`
            break

          case 'FORM_SHARE':
            notificationMessage = `The Form from template ${templateName} has been shared to you by ${userName}.`
            notificationLink = `/form/viewform/${templateId}/${templateName}/${notification.form_id}`
            break

          case 'TASK_UPDATE':
            notificationMessage = `The Task ${taskName} has been updated by ${userName}.`
            notificationLink = `/project-planner?view=board&selectedTaskId=${notification.task_id}`
            break

          case 'PROJECT_USER_INVITE':
            notificationMessage = `${userName} invited you to project ${project?.name}.`
            notificationLink = '/'
            break

          case 'USER_ROLE_UPDATED':
            if (notification.project_id) {
              const projectToken = localStorage.getItem(config.localstorageKeys.PROJECT)
              if (projectToken) {
                const decodedToken = jwtDecode(projectToken)
                if (decodedToken['x-hasura-project-id'] === notification.project_id && new Date(decodedToken.iat * 1000) < new Date(notification.sent_on)) {
                  alert('Your role has been updated in project! Please login again')
                  setTimeout(() => {
                    logout()
                  }, 2000)
                }
              }
            } else {
              const tenantToken = localStorage.getItem(config.localstorageKeys.TENANT)
              const decodedToken = jwtDecode(tenantToken)
              if (new Date(decodedToken.iat * 1000) < new Date(notification.sent_on)) {
                alert('Your role has been updated in tenant! Please login again')
                setTimeout(() => {
                  logout()
                }, 2000)
              }
            }
            continue

          case 'USER_DEACTIVATED':
            if (notification.project_id) {
              const projectToken = localStorage.getItem(config.localstorageKeys.PROJECT)
              if (projectToken) {
                const decodedToken = jwtDecode(projectToken)
                if (decodedToken['x-hasura-project-id'] === notification.project_id && new Date(decodedToken.iat * 1000) < new Date(notification.sent_on)) {
                  alert('You have been deactivated from this project!')
                  setTimeout(() => {
                    logout()
                  }, 2000)
                }
              }
            } else {
              const tenantToken = localStorage.getItem(config.localstorageKeys.TENANT)
              const decodedToken = jwtDecode(tenantToken)
              if (new Date(decodedToken.iat * 1000) < new Date(notification.sent_on)) {
                alert('You have been deactivated from this tenant!')
                setTimeout(() => {
                  logout()
                }, 2000)
              }
            }
            continue

          case 'USER_PASSWORD_UPDATED': {
            const loginToken = localStorage.getItem(config.localstorageKeys.AUTH)
            const decodedToken = jwtDecode(loginToken)
            if (new Date(decodedToken.iat * 1000) < new Date(notification.sent_on)) {
              alert('Session expired due to password change!')
              setTimeout(() => {
                logout()
              }, 2000)
            }
            continue

            // Add more cases for other actions as needed
          }
          default:
            notificationMessage = 'Something went wrong with this notification' // default message
          }
          transformedNotifications.push({
            id,
            readStatus,
            time,
            message: notificationMessage,
            link: notificationLink,
            projectId: projectID
          })
        }
        this.allNotifications = transformedNotifications
        this.loading = false
      }
    },

    toggleHighlight (notificationId) {
      if (!this.highlightedNotificationId || this.highlightedNotificationId !== notificationId) {
        this.highlightedNotificationId = notificationId
      } else {
        this.highlightedNotificationId = null
      }
    }
  }
}
</script>

  <style scoped>
  .notification-container {
    position: relative;
  }
  .notification-box {
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 0px;
    position: absolute;
    top: 50px; /* Adjust the top position as needed */
    right: 10px; /* Adjust the right position as needed */
    min-width: 600px; /* Increase the minimum width */
    max-width: 600px;
    z-index: 20;
    max-height: 350px; /* Add max height to enable scrolling */
    overflow-y: auto; /* Add vertical scroll bar */
  }

  .notification-count {
    position: absolute;
    top: 4px;
    left: 20px;
    background-color: var(--brand-color);
    color: black;
    font-size: 12px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .box {
    min-height: 10px;
  }

  .notification-navbar {
    display: flex;
    justify-content: space-between; /* Aligns children with space in between */
    align-items: center;
    font-weight: bold;
    padding: 10px 0px 10px 0px;
    background-color: var(--brand-color);
    /* border-radius: 5px 5px 0 0; */
    position: sticky;
    top: 0;
    z-index: 20;
  }

  .notification-item {
    padding: 10px;
  }

  .notification-separator {
    border-bottom: 1px solid #ccc;
    margin-top: 5px;
  }

  .notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  a{
    text-decoration: none !important;
    color: inherit !important;
  }

  .notification-message {
    flex-grow: 1;
    word-wrap: break-word; /* Allow long words to wrap */
    max-width: 400px;
  }

  .long-message {
    white-space: normal; /* Reset white-space to allow wrapping */
  }

  .highlighted {
  transition: background-color 0.3s; /* Add a smooth transition effect */
  color: #333;
}

/* Additional styles to improve visibility of highlighted text */
.highlighted .notification-message {
  font-weight: bold;
  /* color: #333; Change the text color as needed */
}

  .notification-time {
    font-size: 12px;
    color: #888;
  }
  .unread-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--brand-color);
  border-radius: 50%;
  margin-right: 4px; /* Adjust as needed */
}
  </style>
