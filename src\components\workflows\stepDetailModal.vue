<template>
  <div class="stepdetail">
    <div v-if="loading" class="stepdetail-loader">
      <loading-circle />
    </div>
    <div v-else class="stepdetail-container">
      <!-- toggle button start  -->
      <div class="stepdetail-header">
        <div class="stepdetail-header-toggle">
        <div class="stepdetail-header-toggle-box">
          <button class="stepdetail-header-toggle-btn" @click="openTab('details')">
            Step Details
          </button>
          <button class="stepdetail-header-toggle-btn" @click="openTab('history')">
            History
          </button>
        </div>
        <div
          class="stepdetail-header-toggle-selection"
          ref="backgroundShade"
        ></div>
      </div>
    </div>
    <!-- toggle button  end   -->

    <!-- step details start  -->
    <div v-show="tab === 'details'" class="stepdetail-body">
      <h4 class="my-3">Step Details</h4>
      <div class="stepdetail-container-data grid-2">
        <div class="grid-3">
          <span>Step Name</span>
          <span>:</span>
          <span>{{ formatedData.name }}</span>
        </div>
        <div class="grid-3">
          <span>Duration</span>
          <span>:</span>
          <span>{{ formatedData.duration }}</span>
        </div>
      </div>

      <div class="stepdetail-container-data grid-2">
        <div class="grid-3">
          <span>Actual start date</span>
          <span>:</span>
          <span>{{ formatedData.actualStartDate }}</span>
        </div>
        <div class="grid-3">
          <span>Actual end date</span>
          <span>:</span>
          <span>{{ formatedData.actualEndDate }}</span>
        </div>
      </div>

      <div class="stepdetail-container-data grid-2">
        <div class="grid-3">
          <span>Planned start date</span>
          <span>:</span>
          <span>{{ formatedData.plannedStartDate }}</span>
        </div>
        <div class="grid-3">
          <span>Planned end date</span>
          <span>:</span>
          <span>{{ formatedData.plannedEndDate }}</span>
        </div>
      </div>
      <div class="stepdetail-container-data grid-2">
        <div class="grid-3">
          <span>Escalation threshold</span>
          <span>:</span>
          <span>{{ formatedData.escalation }}</span>
        </div>

        <div class="grid-3">
          <span>Department</span>
          <span>:</span>
          <span>{{ formatedData.department }}</span>
        </div>
      </div>

      <!--  ESCALATION HOSTORY STARTS HERE  -->

      <div class="my-2">
        <div class="v-center flex-start gap-2 my-4" >
          <h4 >Escalation History</h4>
          &nbsp;:  &nbsp;
          <i  v-if="!historyData.length" class="text-muted"> History is not available</i>
        </div>
        <div class="history-map mt-2">
          <div
            class="history-map-parent"
            v-for="item in historyData"
            :key="item.id"
          >
            <div class="p-5 history-map-icon-round">
              {{ item.escalation_level }}
              <div class="history-map-icon-vl"></div>
              <div class="history-map-icon-arrow"></div>
            </div>
            <div class="history-map-hr"></div>
            <div class="history-map-content-box">
              <div class="history-map-item">
                <span>Status</span>
                <span>:</span>
                <span
                  :class="{
                    'v-center escalation-status': true,
                    pending: item.response === 'PENDING',
                    completed: item.response === 'COMPLETED',
                  }"
                >
                  {{ item.response }}
                </span>
              </div>
              <div class="history-map-item">
                <span>Escalted By</span>
                <span>:</span>
                <span v-overflow-tooltip class="elipsis-text">
                  {{
                    " " +
                    item.escalated_by_user.first_name +
                    " " +
                    item.escalated_by_user.last_name
                  }}</span
                >
              </div>
              <div class="history-map-item">
                <span>Escalted to</span>
                <span>:</span>
                <span v-overflow-tooltip class="elipsis-text">
                  {{
                    item.escalated_to_user.first_name +
                    " " +
                    item.escalated_to_user.last_name
                  }}</span
                >
              </div>
              <div class="history-map-item">
                <span>Escalted on</span>
                <span>:</span>
                <span v-overflow-tooltip class="elipsis-text">
                  {{ item.escalated_at | timeStampToDateTime }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
        <!--  ESCALATION HOSTORY ENDS HERE  -->
    </div>
      <!-- step details end  -->

      <!-- history start  -->
      <div v-if="loading">
        <loading-circle />
     </div>
     <i v-else-if="!Object.keys(wfHistory).length && tab === 'history'" class="stepdetail-history-box p-3">
        History is not available
     </i>
    <div v-else  v-show="tab === 'history'"  class="stepdetail-history-box">
        <div  v-for="(dateArray, index) in wfHistory" class="stepdetail-history-body" :key="index" >
      <div class="stepdetail-history-body-dateBox">
        <div class="stepdetail-history-body-date">   <img
          src="~@/assets/images/icons/calenderGrey.svg"
          width="12px"
          alt=""
        /> {{dateArray[0].timestamp | timeStampToDate }}</div>
        <hr class="stepdetail-history-body-date-line" />
      </div>
      <div class="stepdetail-history-body-elementsBox">
        <history-element  v-for="(item, index) in dateArray" :key="index" :data="item" :elementColor="'238, 159, 214'" />
      </div>
    </div>
  </div>
       <!-- history end  -->
    </div>
  </div>
</template>
<script>
import { getWfHistory, getworkflowEscalationHistory } from '@/api'
import loadingCircle from '@/components/common/loadingCircle.vue'
import { alert } from '@/plugins/notification'
import { timeStampToDateTime } from '@/filters/dateFilter'
import HistoryElement from '@/views/FORM/historyElement.vue'
import config from '@/config'
export default {
  name: 'StepDetail',
  components: { loadingCircle, HistoryElement },
  props: {
    stepData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    instanceId: {
      type: String,
      default: ''

    }
  },
  filters: {
    timeStampToDateTime,
    timeStampToDate (timestamp) {
      return new Date(timestamp).toLocaleDateString('en-US', {
        weekday: 'long', // Sunday
        month: 'short', // Jul
        day: 'numeric' // 20
      })
    }
  },
  data: () => ({
    loading: false,
    selectedTab: 'history',
    historyData: [],
    tab: 'details',
    wfHistory: {}
  }),
  mounted () {
    this.getHistoryData()
    this.getWorkflowHistory()
  },
  computed: {
    formatedData () {
      return {
        id: this.stepData?.id,
        name: this.stepData.attrs.text.text,
        duration: this.stepData.duration + ' hours',
        escalation: this.stepData.escalation ?? '--',
        plannedStartDate:
          this.stepData.attrs.PlanendDateText?.text.split('-')[0] ?? '--',
        plannedEndDate:
          this.stepData.attrs.PlanendDateText?.text.split('-')[1] ?? '--',
        actualStartDate:
          this.stepData.attrs.ActualDateText?.text.split('-')[0] ?? '--',
        actualEndDate:
          this.stepData.attrs.ActualDateText?.text.split('-')[1] ?? '--',
        department: this.stepData.departmentName
      }
    }
  },
  watch: {
    'stepData.id' () {
      this.getHistoryData()
    }
  },
  methods: {
    getHistoryData () {
      this.loading = true
      getworkflowEscalationHistory(this.stepData?.id, this.instanceId)
        .then((res) => {
          this.historyData = res.workflow_step_escalation_history
          this.loading = false
        })
        .catch((err) => {
          console.log(err)
          alert('unable to fetch escaltion history')
        })
    },
    openTab (type) {
      if (type === 'details') {
        this.$refs.backgroundShade.style.left = '0px'
      } else {
        this.$refs.backgroundShade.style.left = '100px'
      }
      this.tab = type
    },
    getWorkflowHistory () {
      this.loading = true
      getWfHistory(this.instanceId).then((res) => {
        this.loading = false
        this.wfHistory = {}
        const history = res.message.data.filter(item => item.stepId === this.stepData.id)
        for (const item of history ?? []) {
          const date = new Date(item.timestamp)
          const dateStr = date.toISOString().split('T')[0]
          switch (item.state) {
          case 'TRANSITIONED':
            item.elementColor = config.elementColor.purple
            break
          case 'STARTED':
            item.elementColor = config.elementColor.green
            break
          case 'CLOSED':
            item.elementColor = config.elementColor.orange
            break
          case 'ASSIGNED':
            item.elementColor = config.elementColor.blue
            break
          default:
            item.elementColor = config.elementColor.blue
            break
          }

          if (!this.wfHistory[dateStr]) {
            this.wfHistory[dateStr] = []
          }
          this.wfHistory[dateStr].push(item)
        }
      })
    }
  }
}
</script>
<style lang="scss">
$selection-width: 100px;
$selection-height: 28px;
$greyShade-color: #b8b7b7;
.modal {
  // this is to remove default  backgroynd blur in modal view
  backdrop-filter: blur(0px) !important;
}
.stepdetail {
  min-width: 30vw;
  min-height: 240px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
  align-items: center;
  &-body{
-webkit-animation:slide-right .5s cubic-bezier(.25,.46,.45,.94) alternate both;
animation:slide-right .5s cubic-bezier(.25,.46,.45,.94) alternate both;
}
&-history{
&-box {
// -webkit-animation: slide-left 0.5s ease-in 1 alternate;
// animation: slide-left 0.5s ease-in 1 alternate;
width:100%;
height:100%;
  }
  &-body {
    margin-top: 1.5em;
    padding:5px;
    &-dateBox {
      display: flex;
      gap: 10px;
      align-items: center;
      font-size: 0.8em;
    }
    &-date {
      display: flex;
      align-items:center;
      gap: 5px;
      color: #b5b4b4;
    }
    &-date-line {
      flex-grow: 1;
      border: none;
      height: 0.5px;
      background-color: #cccccc81;
    }
    &-elementsBox {
    padding: 5px;
    overflow:auto;
    }
  }
}

  &-loader {
    width: 100%;
    flex-grow: 1;
    height: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    &-data {
      margin-top: 1rem;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 10px;
      align-items: center;
      & .grid-3 {
        display: grid;
        grid-template-columns: 3fr 1fr 3fr;
        align-items: center;
        // justify-items: center;
        gap: 5px;
      }
    }
  }
  &-header {
    padding-inline: 0.5em;
    display: flex;
    justify-content: center;
    background-color: var(--bg-color);
    position: sticky;
    top: 0;
    z-index: 2;

    &-toggle {
      background-color:rgb($greyShade-color, .3);
      position: relative;
      height: calc($selection-height + 2px);
      width: calc($selection-width*2);
      border-radius: 4px;
      &-box {
        position: absolute;
        z-index: 1;
        border: 0.4px solid rgb(219, 214, 214);
        height: calc($selection-height + 2px);
        width: calc($selection-width*2);
        border-radius: 4px;
        display: flex;
        background: transparent;
        font-size: 0.85em;
      }
      &-btn {
        all: unset;
        width: $selection-width;
        height: $selection-height;
        text-align: center;
        cursor: pointer;
        background: transparent;
      }
      &-selection {
        position: absolute;
        width: calc($selection-width + 2px);
        background-color: white;
        left: 0;
        top: 0;
        bottom: 0;
        height: calc($selection-height + 2px);
        z-index: 0;
        border-radius: 4px;
        transition: all 0.3s ease;
      }
    }
    &-text {
      font-size: 0.85em;
      color: rgb(164, 162, 162);
      height: $selection-height;
      display: flex;
      align-items: center;
    }
  }
}

.history-map {
  max-width: 400px;
  &-item {
    display: grid;
    grid-template-columns: 3fr 1fr 4fr;
    align-items: center;
    // justify-items: center;
    gap: 5px;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    & :first-child {
      color: rgba(95, 94, 94, 0.923);
    }
  }
  &-icon {
    &-vl {
    position: absolute;
    height: 115px;
    border: 1px solid #a5a4a4;
    border-width: 0px 0 0 1px;
    border-style: dashed;
    top: 40px;
      // z-index: -1
    }
    &-arrow {
      padding: 5px;
      transform: rotate(45deg);
      transform-origin: center;
      border: 1.5px solid rgb(165, 164, 164);
      border-width: 1px 0px 0px 1px; // background-color: rgb(38, 39, 38);
      position: absolute;
      top: 42px;
    }
    &-round {
      border: 0.5px solid rgb(159 191 219 / 60%);
      border-radius: 50%;
      width: 15px;
      height: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      font-size: 1.2rem;
      font-weight: 500;
      background-color: rgba(219, 227, 240, 0.595);
    }
  }
  &-parent {
    display: flex;
    align-items: center;
    margin-top: 5px;
    &:last-child .history-map-icon-round .history-map-icon-vl {
      display: none;
    }
    &:last-child .history-map-icon-round .history-map-icon-arrow {
      display: none;
    }
  }
  &-content-box {
    padding: 10px;
    width: 300px;
    height: 150px;
    border-radius: 6px;
    border: 0.5px solid rgba(217, 217, 217, 0.6);
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    &:hover {
      box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    }
  }
  &-hr {
    border: 1px solid #a5a4a4;
    flex-grow: 1;
    min-width: 50px;
    border-width: 1px 0 0 0;
    border-style: dotted;
  }
}

.escalation-status {
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 600;
  display: inline-block;
  text-align: center;
}
.completed {
  background-color: rgba(0, 128, 0, 0.1);
  color: green;
}

.pending {
  background-color: rgba(255, 165, 0, 0.1);
  color: orange;
}
@-webkit-keyframes slide-right{
  0%{
    -webkit-transform:translateX(-100px);
  transform:translateX(-100px)}
  100%{-webkit-transform:translateX(0);
  transform:translateX(0)
}
  }
  @keyframes slide-right{
    0%{-webkit-transform:translateX(-100px);
    transform:translateX(-100px)}
    100%{-webkit-transform:translateX(0);transform:translateX(0)}
    }

    @-webkit-keyframes  slide-left{
      0%{
        -webkit-transform:translateX(100px);
        transform:translateX(100px);
        overflow-x: hidden;
      }
      100%{
        -webkit-transform:translateX(0);
        transform:translateX(0);
        overflow-x: hidden;
      }
      }
      @keyframes  slide-left{0%{
        -webkit-transform:translateX(100px);
        transform:translateX(100px);
             overflow-x: hidden;
      }
      100%{
        -webkit-transform:translateX(0);
        transform:translateX(0);
        overflow-x: hidden;
        }}

/* Phones (up to 480px) */
@media (max-width: 480px) {
.stepdetail {
  max-width: 95vw;
  max-height: 90vh;
   &-container-data{
      display: grid;
      grid-template-columns: 1fr;
      grid-gap: 10px;
      align-items: center;
  }
}
}

/* Tablets (portrait and small tablets: 481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
.stepdetail {
  max-width: 85vw;
  max-height:80vh;
}
}

/* Tablets (standard: 768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
.stepdetail {
  max-width: 70vw;
  max-height:70vh;
}
}

/* Laptops (1025px - 1280px) */
@media (min-width: 1025px) and (max-width: 1280px) {
  .stepdetail {
  max-width: 60vw;
  max-height:60vh;
}

}

/* Desktops (1281px - 1600px) */
@media (min-width: 1281px) and (max-width: 1600px) {
  .stepdetail {
  max-width: 45vw;
  max-height:50vh;
}
}

/* Big Screens (1601px and up) */
@media (min-width: 1601px) {
  .stepdetail {
  max-width: 40vw;
  max-height:50vh;
}
}

</style>
