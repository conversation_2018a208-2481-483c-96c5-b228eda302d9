<template>
  <div class="create-bom s">
    <div class="material" :style="{ 'max-width': showProductCode ? '55%' : 'none' }">
      <div class="material-bar">
        <div class="material-bar--title">Add Material</div>
        <div class="input-group search">
          <input type="text" v-model="searchKeyWord" @input="updateOnSearch" placeholder=" Filter by Name, ID, Product Code, and Description"/>
        </div>
      </div>
      <filterTag :tags="getTags" @new-tag="addNewTag" :type="1"
      @update-tags="updateTags" title="Add tags to filter"
      />
      <div class="material-table dtx-table mb-4">
        <table style="min-width: 800px">
          <thead>
            <tr>
              <th></th>
              <th>Product Code</th>
              <th>Material ID</th>
              <th>PLM ID</th>
              <th>ERP ID</th>
              <th>UOM</th>
              <th>Description</th>
              <th>Lead Time</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody v-if="!materialMasterLoading">
            <tr v-for="item in materialMasterData" :key="item.material_id">
              <td v-if="productCode===item.product_code"></td>
              <td v-else @click="addToBom(item)">
                <img v-if="addedBomItems.includes(item.id)" src="~@/assets/images/icons/checked-icon.svg" alt="">
                <img v-else src="~@/assets/images/icons/unchecked-icon.svg" alt="">
              </td>
              <td v-overflow-tooltip>{{ item.product_code_name ||  item.material_product_code?.product_code || "--" }}</td>
              <td v-overflow-tooltip>{{ item.custom_material_id || "--" }}</td>
              <td v-overflow-tooltip>{{ item.plm_material_id || "--" }}</td>
              <td v-overflow-tooltip>{{ item.erp_material_id || "--" }}</td>
              <td v-overflow-tooltip>{{ item.material_unit_details.name || "--" }}</td>
              <td v-overflow-tooltip>{{ item.material_description || "--" }}</td>
              <td>{{ item.lead_time || "--" }}</td>
              <td>
              <img @click="openDetailModal(item.id)" class="mx-2" width="16" src="~@/assets/images/eye.svg" alt="" />
              </td>
            </tr>
          </tbody>
        </table>
        <div class="center p-6">
          <loading-circle v-if="materialMasterLoading" />
        </div>
      </div>
      <div class="pagination-footer mt-4">
      <pagination
        :length="totalCount"
        :perPage="perPage"
        :pageNumber="pageNumber"
        @selectPage="selectPage"
      />
      <span class="pagination-footer-total">Total Materials :  &nbsp;  <b> {{ totalCount }}</b></span>
    </div>
      <modal
      @close="closeDetailModal"
      :open="detailModalObject.isOpen"
      title="Material Detail View"
      :closeOnOutsideClick="true"
    >
      <detail-material-master
        v-if="detailModalObject.selectedId !== -1"
        :materialMaster="detailModalObject.detailMaterialData"
        :loading="detailModalObject.loading"
      />
    </modal>
    </div>
    <div class="bom-detail" :style="{ 'width': showProductCode ? '0' : 'auto', 'flex-grow': showProductCode ? '1' : 'unset' }">
      <div class="bom-detail-bar">
        <div class="bom-detail-bar--title">Create BOM</div>
      </div>
      <div class="input-group mt-2" :class="{'addBomForm': moreInput}">
      <div class="input-group mt-2">
        <label>Product Code</label>
        <input type="text" disabled v-model="productName" />
      </div>
      <div class="input-group mt-2">
        <label>Bom Name</label>
        <input type="text" v-model="bomName" />
      </div>
      <effectiveDate @date-selected="createEffectiveDate"/>
      <div class="input-group mt-3  underline pointer" v-if="!moreInput">
        <label @click="toggleMore">Add Standard Bom Form</label>
      </div>
      <div class="mt-3" v-show="moreInput">
        <std-Bom-Form ref="stdBomForm" @stdBomForm="handleBomForm" :stdFormValues="standardBomForm" @validationErrors="handleValidationErrors" :createForm="true"/>
      </div>
    </div>
    <div class="input-group mt-3 underline pointer" v-if="moreInput">
        <label @click="toggleMore">Show Less</label>
      </div>
      <div class="bom-detail-item dtx-table mt-3">
        <div class="bom-detail-item--no-material" v-if="!bom_items.length">
          Add at least One Material
        </div>
        <table v-else >
          <thead>
            <tr>
              <th>Material ID</th>
              <th class="elipsis-text" style="width: 100px" >Description</th>
              <th >Quantity</th>
              <th>Unit Size</th>
              <th>Material Cost</th>
              <th>Material Sale Price</th>
              <th>Type</th>
              <th>Bom</th>
              <th></th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in bom_items" :key="item.material_id + index">
              <td>{{ item.custom_material_id || "--" }}</td>
              <td >
                <div v-tooltip="item.material_description" class="elipsis-text" style="width: 120px" >{{ item.material_description || "--" }}
                </div>
              </td>
              <td>
                <input
                  type="number"
                  v-model.number="bom_items[index].quantity"
                  @change="validateQuantity(index)"
                  min="1"
                  @keydown="changeNumber"
                />
              </td>
              <td>
                <input
                  type="number"
                  v-model.number="bom_items[index].unit_size"
                  @change="validateUnitSize(index)"
                  :min="1"
                  @keydown="changeNumber"
                />
              </td>
              <td>
                <input
                class="resized-box"
                  type="number"
                  v-model.number="bom_items[index].material_unit_cost"
                  @change="bom_items[index].overridden_fields.unit_cost = true"
                  min="0"
                  @keydown="changeNumber"
                /></td>
              <td>
                <input
                class="resized-box"
                  type="number"
                  v-model.number="bom_items[index].material_unit_sale_price"
                  @change="bom_items[index].overridden_fields.sale_price = true"
                  min="0"
                  @keydown="changeNumber"
                /></td>
              <td>
              <select class="custom-list-select" @change="bom_items[index].overridden_fields.material_group = true" v-model="bom_items[index].material_group" name="" id="">
                <option v-for="options in filteredCustomList(index)" :key="options.id" :value="options.id">
                {{ options.name }}</option>
              </select>
              </td>
              <!-- <td v-else >
              <select class="custom-list-select" @change="bom_items[index].overridden_fields.resource_group = true" v-model="bom_items[index].resource_group" name="" id="">
                <option v-for="options in resourceList" :key="options.id" :value="options.id">
                {{ options.name }}</option>
              </select>
              </td> -->
              <td >
                <div v-if="!item.associated_bom_id" >
                  <img
                    v-if="item.product_code"
                    :class="{
                      'add-bom': true,
                      'active': true,
                      'ml-2': true
                    }"
                    v-tooltip="'Associate Bom'"
                    src="~@/assets/images/icons/add-new-icon.svg"
                    width="20px"
                    alt=""
                    @click="openAssociateBomModal(item, index)"
                  />
                  <img
                    v-else
                    :class="{
                      'add-bom': true,
                      'active': false,
                      'ml-2': true
                    }"
                    src="~@/assets/images/icons/add-new-icon.svg"
                    width="20px"
                    alt=""
                  />
                </div>
                <div
                  v-else
                  class="associated-bom"
                  v-tooltip="item.associated_bom_name"
                  @click="openAssociateBomModal(item, index)"
                >
                  {{ item.associated_bom_name }}
                </div>
              </td>
              <td>
                <div class="action" @click="openDrawer(item, index)">
                  <img v-tooltip="'Add custom fields'" class="pointer mx-1" src="~@/assets/images/icons/forms-doc.svg" alt=""  width="12" />
                </div>
              </td>
              <td>
                <div @click="openRemarkModal(index)">
                  <img class="pointer" src="~@/assets/images/icons/add-remarks-icon.svg" width="16px" alt="" />
                </div>
              </td>
              <td>
                <div class="action" @click="removeBomItem(item, index)">
                  <img class="pointer" src="~@/assets/images/close.png" width="16px" alt="" />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="v-center my-4 flex-end">
          <button class="btn btn-black ml-2" @click="cancelCreateNew">
            Cancel
          </button>
          <button class="btn ml-2" :disabled="bom_items.length == 0 || disableSaveButton" @click="save">Save</button>
        </div>
    </div>
    <div v-if="drawer" class="drawer" :class="drawer ? 'open' : 'close'">
      <div class="project-form-container">
        <create-material-form  @cancel="closeDrawer" @create-material="handleCreateMaterial"  :createMaterialValues="standardMaterialData.newMaterialData"> </create-material-form>
      </div>
    </div>
    <modal
      title="Add remarks"
      :open="addRemarkModalObject.isOpen"
      @close="addRemarkModalObject.isOpen = false"
    >
    <div class="p-4">
      <div class="input-group mt-2">
          <label>Remarks</label>
          <textarea class="remarks" v-model="addRemarkModalObject.remarks">
          </textarea>
        </div>
        <div class="v-center my-4 flex-end">
          <button class="btn btn-black ml-2" @click="addRemarkModalObject.isOpen = false">
            Cancel
          </button>
          <button class="btn ml-2" @click="saveRemarks">Save</button>
        </div>
    </div>
    </modal>
    <modal
      :open="associateBom.show"
      @close="closeAssociateBomModal"
      title="Associate Bom"
    >
      <select-bom
        v-if="associateBom.show"
        :product_code="associateBom.product_code"
        :selectedBomId="associateBom.associated_bom_id"
        :parentBomProdCode="productCode"
        @selectBom="selectAssociateBom"
        @close="closeAssociateBomModal"
        :showCloseButton="true"
      />
    </modal>
  </div>
</template>

<script>
import {
  GetMaterialMasterForBom,
  CreateProductBom, GetProductCode,
  GetSingleMaterialMasterData,
  MaterialGroup,
  GetBomForSwitching,
  ResourceGroup
} from '@/api'
import loadingCircle from '../../../components/common/loadingCircle.vue'
import Pagination from '../../../components/common/pagination.vue'
import { debounce } from '@/utils/debounce'
import { alert, success } from '@/plugins/notification'
import Modal from '../../../components/common/modal.vue'
import SelectBom from '../../../components/bom/common/selectBom.vue'
import filterTag from '../../../components/common/filterTag.vue'
import { mapGetters, mapMutations } from 'vuex'
import DetailMaterialMaster from '../../../components/materialMaster/detailMaterialMaster.vue'
import Loader from '@/plugins/loader'
import effectiveDate from '../../../components/common/effectiveDate.vue'
import createMaterialForm from '../bomForm/createMaterialForm.vue'
import stdBomForm from '../bomForm/stdBomForm.vue'
import { restrictKeys } from '@/utils/validations'
export default {
  components: { loadingCircle, Pagination, Modal, SelectBom, filterTag, DetailMaterialMaster, effectiveDate, createMaterialForm, stdBomForm },
  name: 'CreateProductBom',
  data () {
    return {
      disableSaveButton: false,
      resourceList: [],
      customList: [],
      materialMasterData: [],
      materialMasterLoading: false,
      pageNumber: 1,
      perPage: 10,
      totalCount: 0,
      searchKeyWord: '',
      updateOnSearch: null,
      bomName: '',
      productName: '',
      productCode: '',
      bom_items: [],
      associateBom: {
        show: false,
        product_code: null,
        associated_bom_id: null,
        associated_bom_name: null,
        associated_bom_version: null,
        associated_bom_version_id: null,
        item: null,
        index: null
      },
      detailModalObject: {
        isOpen: false,
        selectedId: -1,
        detailMaterialData: {}
      },
      addEffectiveDate: null,
      addRemarkModalObject: {
        isOpen: false,
        selectedIndex: -1,
        remarks: ''
      },
      drawer: false,
      standardMaterialData: {
        newMaterialData: null,
        index: null
      },
      moreInput: false,
      standardBomForm: [],
      validationError: []
    }
  },
  computed: {
    ...mapGetters('tag', ['getTags', 'bomList']),
    showProductCode () {
      return ((this.$route.path.includes('/bom') && this.$route.path.includes('/edit')) || (this.$route.path.includes('/bom') && this.$route.path.includes('/new')))
    },
    addedBomItems () {
      return this.bom_items.map((item) => item.material_id)
    }
  },
  watch: {
    '$store.state.tag.tags': function () {
      this.fetchMaterialMasterData()
    }
  },
  methods: {
    filteredCustomList (index) {
      const materialType = this.bom_items[index].type
      return this.customList.filter(option => option.type === materialType)
    },
    changeNumber (event) {
      if (['e', 'E', '+', '-'].includes(event.key)) {
        restrictKeys(event)
      }
    },
    handleValidationErrors (errors) {
      this.validationError = errors // Store the errors to display
    },
    toggleMore () {
      this.moreInput = !this.moreInput
    },
    handleBomForm (data) {
      this.standardBomForm = data || null
    },
    openDrawer (item, index) {
      this.standardMaterialData.newMaterialData = item
      this.standardMaterialData.index = index
      this.drawer = !this.drawer
    },
    closeDrawer () {
      this.drawer = false
    },
    handleCreateMaterial (obj) {
      this.bom_items[this.standardMaterialData.index].overridden_material_fields =
      obj || null
      this.closeDrawer()
    },
    openDetailModal (id) {
      const loader = new Loader()
      loader.show()
      GetSingleMaterialMasterData({ id }).then(res => {
        loader.hide()
        this.detailModalObject.isOpen = true
        this.detailModalObject.selectedId = id
        this.detailModalObject.detailMaterialData = res.core_material_master[0]
      }).finally(() => {
        loader.hide()
      })
    },
    closeDetailModal () {
      this.detailModalObject.isOpen = false
      this.detailModalObject.selectedId = -1
    },
    save () {
      if (!this.productCode) {
        alert('Product Code is required')
        return
      }
      if (!this.bomName) {
        alert('BOM Name is required')
        return
      }
      this.$refs.stdBomForm.validateInputData()
      if (this.validationError.length > 0) {
        this.validationError.forEach(error => alert(error))
        this.validationError = []
        return
      }
      const unitSize = this.bom_items.map((item) => item.material_id + '-' + item.unit_size)
      const uniqueUnitSize = [...new Set(unitSize)]
      if (uniqueUnitSize.length !== unitSize.length) {
        alert('Unit Size should not be same for same material')
        return
      }
      this.disableSaveButton = true
      const loader = new Loader()
      loader.show()
      const bomItems = this.bom_items.map((item) => {
        const result = {
          material_id: item.material_id,
          quantity: item.quantity,
          total_price: item.total_price,
          material_unit_cost: item.overridden_fields.unit_cost ? item.material_unit_cost : null,
          material_unit_sale_price: item.overridden_fields.sale_price ? item.material_unit_sale_price : null,
          material_group: item.material_group,
          unit_size: item.unit_size,
          assoc_bom_version_id: item.associated_bom_version_id,
          remarks: item?.remarks || null
        }
        if (item.overridden_material_fields && Object.keys(item.overridden_material_fields).length > 0) {
          result.overridden_custom_material_fields = item.overridden_material_fields
        }
        return result
      })
      // unit_size should not be same for same material
      CreateProductBom(
        this.productCode,
        this.bomName,
        bomItems,
        null,
        null,
        this.addEffectiveDate,
        this.standardBomForm
      ).then((res) => {
        loader.hide()
        if (res.insert_bom?.message === 'Bom created Successfully!') {
          success('BOM Created Successfully')
          GetBomForSwitching(this.productCode, this.bomName).then(res => {
            const bomId = res.core_bom[0].id
            const versionId = res.core_bom[0].bom_versions[0].id
            this.$router.push(
        `/bom/product/${this.productCode}/bom/${bomId}${
          versionId
            ? `?bomVersionId=${versionId}`
            : ''
        }`
            )
          })
          // this.cancelCreateNew()
        } else {
          loader.hide()
          alert(res.insert_bom?.message ?? 'Something went wrong')
        }
      }).catch((e) => {
        loader.hide()
        if (e.message.includes('Bom name already exists')) {
          alert('BOM Name already exists')
        } else {
          alert(e?.message ?? 'Something went wrong')
        }
      }).finally(() => {
        loader.hide()
        this.disableSaveButton = false
      })
    },
    openAssociateBomModal (item, index) {
      this.associateBom.show = true
      this.associateBom.item = item
      this.associateBom.product_code = item.product_code
      this.associateBom.associated_bom_id = item.associated_bom_id
      this.associateBom.associated_bom_name = item.associated_bom_name
      this.associateBom.associated_bom_version = item.associated_bom_version
      this.associateBom.associated_bom_version_id = item.associated_bom_version_id
      this.associateBom.index = index
    },
    closeAssociateBomModal (bom) {
      this.associateBom.show = false
      this.associateBom.item = null
      this.associateBom.associated_bom_id = null
      this.associateBom.associated_bom_name = null
      this.associateBom.associated_bom_version = null
      this.associateBom.associated_bom_version_id = null
      this.associateBom.index = null
    },
    selectAssociateBom (bom) {
      if (bom.id === this.associateBom.associated_bom_id) {
        return
      }
      this.bom_items[this.associateBom.index].associated_bom_id = bom?.id || null
      this.bom_items[this.associateBom.index].associated_bom_name = bom?.name || null
      this.bom_items[this.associateBom.index].associated_bom_version = bom?.version || null
      this.bom_items[this.associateBom.index].associated_bom_version_id = bom?.versionId || null
      this.bom_items[this.associateBom.index].material_unit_cost =
        bom?.total_cost || 0
      this.bom_items[this.associateBom.index].material_unit_sale_price =
        bom?.sale_price || 0
    },
    addToBom (item) {
      item.type = parseInt(item.type)
      this.bom_items.push({
        material_name: item.material_name,
        material_id: item.id,
        type: item.type,
        custom_material_id: item.custom_material_id,
        quantity: item.quantity || 1,
        total_price: 0,
        unit_size: item.unit_size || 1,
        material_description: item.material_description,
        product_code: item.product_code,
        material_group: item.material_group,
        resource_group: item.resource_group,
        associated_bom_id: null,
        associated_bom_name: null,
        associated_bom_version: null,
        associated_bom_version_id: null,
        material_unit_cost: item.unit_cost || 0,
        material_unit_sale_price: item.unit_sale_price || 0,
        formId: item.core_form ? item.core_form.id : null,
        overridden_fields: {
          unit_cost: false,
          sale_price: false,
          material_group: false,
          resource_group: false
        }
      })
    },
    removeBomItem (item, index) {
      this.bom_items.splice(index, 1)
    },
    selectPage (page) {
      this.pageNumber = page
      this.fetchMaterialMasterData()
    },
    fetchMaterialMasterData () {
      this.materialMasterLoading = true
      const filter = {
        jump: (this.pageNumber - 1) * this.perPage,
        perPage: this.perPage,
        searchKeyword: this.searchKeyWord ? `%${this.searchKeyWord}%` : undefined
      }
      const self = this
      if (this.getTags.length > 0) {
        const body = {
          jump: (this.pageNumber - 1) * this.perPage,
          perPage: this.perPage,
          searchKeyword: this.searchKeyWord ? `%${this.searchKeyWord}%` : undefined,
          tagId: this.getTags.length ? this.getTags[this.getTags.length - 1]?.id : undefined
        }
        GetMaterialMasterForBom(body).then((res) => {
          self.materialMasterData = res.core_material_master
          self.totalCount = res.core_material_master_aggregate.aggregate.count
          this.materialMasterLoading = false
        }).catch((err) => {
          console.log(err)
        })
      } else {
        GetMaterialMasterForBom(filter).then((res) => {
          self.materialMasterData = res.core_material_master
          self.totalCount = res.core_material_master_aggregate.aggregate.count
          this.materialMasterLoading = false
        })
      }
    },
    cancelCreateNew () {
      this.$router.push(`/bom/product/${this.$route.params.productCode}`)
    },
    updateProductCode () {
      this.productCode = this.$route.params.productCode
      GetProductCode(this.productCode).then((res) => {
        this.productName = res.product_code_by_pk.product_code
      })
    },
    createEffectiveDate (dateValue) {
      this.addEffectiveDate = dateValue
    },
    ...mapMutations('tag', ['addNewTag', 'updateTags']),
    openRemarkModal (index) {
      this.addRemarkModalObject.selectedIndex = index
      this.addRemarkModalObject.remarks = this.bom_items[index]?.remarks || ''
      this.addRemarkModalObject.isOpen = true
    },
    saveRemarks () {
      this.bom_items[this.addRemarkModalObject.selectedIndex].remarks = this.addRemarkModalObject.remarks
      this.addRemarkModalObject.isOpen = false
    },
    validateUnitSize (index) {
      if (this.bom_items[index].unit_size <= 0) {
        alert('Unit size smaller than 0 is restricted.')
        this.bom_items[index].unit_size = 1
      }
    },
    validateQuantity (index) {
      if (this.bom_items[index].quantity % 1 !== 0 || this.bom_items[index].quantity <= 0) {
        alert('Quntity should be a natural number')
        if (this.bom_items[index].quantity <= 0) { this.bom_items[index].quantity = 1 } else { this.bom_items[index].quantity = Math.round(this.bom_items[index].quantity) }
      }
    },
    keyPress (e) {
      if (e instanceof KeyboardEvent && e.code === 'Escape') {
        this.cancelCreateNew()
      } else if (e instanceof KeyboardEvent && e.code === 'Enter') {
        this.save()
      }
    }
  },
  mounted () {
    ResourceGroup('Resource Group').then((res) => {
      res.custom_list_values.map((item) => {
        this.customList.push({
          name: item.name,
          id: item.id,
          type: 2
        })
      })
    })
    MaterialGroup().then(res => {
      res.custom_list_values.map((item) => {
        this.customList.push({
          name: item.name,
          id: item.id,
          type: 1
        })
      })
    })
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.fetchMaterialMasterData()
    }, 300)
    this.fetchMaterialMasterData()
    this.updateProductCode()
  },
  created () {
    document.body.addEventListener('keydown', this.keyPress)
  },
  beforeDestroy () {
    document.body.removeEventListener('keydown', this.keyPress)
  }
}

</script>

<style lang="scss" scoped >
.custom-list-select {
  max-width: 100%;
  min-width: 100%;
  // margin: 0 0 0 10px;
  margin-right: auto;
  font-size: 12px;
  background-color: var(--brand-light-color);
  line-height: 1;
  border: 1px solid var(--brand-color);
  border-radius: 4px;
  padding: 2px 1px;
}
.create-bom {
  height: 100%;
  display: flex;
  .material {
    width: calc(100% - 500px);
    height: 100%;
    padding-right: 10px;
    &-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--brand-color);
      padding: 4px 10px;
      margin-bottom: 10px;
      &--title {
        font-size: 18px;
        font-weight: 500;
      }
      input {
        background-color: white;
      }
    }
    &-table {
      height: calc(100% - 150px);
      overflow-y: auto;
      th,
    td {
      max-width: 80px; /* Set your desired max width for each column */
        overflow: hidden; /* Prevents content from overflowing */
        text-overflow: ellipsis; /* Adds ellipsis (...) for overflowing text */
        white-space: nowrap;
    }
    }
  }
  .bom-detail {
    background-color: var(--bg-color);
    height: 60rem;
    width: 500px;
    padding: 10px;
    &-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--brand-color);
      margin: -10px -10px 10px -10px;
      padding: 4px 10px;
      &--title {
        font-size: 18px;
        font-weight: 500;
      }
    }
    &-item {
      margin-top: 12px;
      max-height: 300px;
      overflow-y: auto;
      &--no-material {
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        border: var(--border);
        padding: 10px;
        color: var(--brand-color-1);
      }
    }
    .add-bom {
      opacity: 0.4;
      cursor: not-allowed;
      &.active {
        opacity: 1;
        cursor: pointer;
      }
    }
    .associated-bom {
      max-width: 80px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:hover {
        cursor: pointer;
        text-decoration: underline;
        color: var(--blue-color);
      }
    }
  }
}
.addBomForm {
    height: 450px;
    overflow-y: auto;
  }
.pagination-footer{
width:100%;
display: flex;
justify-content: space-between;
  &-total{
    margin-top: 16px;
    display: flex;
  background: #ffc56761;
  padding: 0.5rem 0.5rem 0.5rem;
  border: 1px solid var(--brand-color);
  border-radius: 0.2em;
  width: fit-content;
  padding-inline: 10px;
  }
}
.remarks {
  min-width: 50rem;
  min-height: 6rem;
  max-height: 70vh;
  max-width: 70rem;
}
.resized-box{
width: 100px !important;
}
</style>
