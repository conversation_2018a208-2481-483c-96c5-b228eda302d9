<template>
  <div class="stepper-header">
    <div
      v-for="(label, index) in steps"
      :key="index"
      class="step-item"
    >
      <div
        :class="[
          'step-label',
          {
            active: index <= currentStepIndex,
            clickable: label !== 'Completed' && !isPreview
          }
        ]"
        @click="handleStepClick(label, index)"
      >
        {{ label }}
      </div>

      <!-- Divider line except after the last step -->
      <div
        v-if="index < steps.length - 1"
        :class="['divider', { active: index < currentStepIndex }]"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    step: {
      type: String,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      steps: ['Basic Info', 'Customise Fields', 'Completed']
    }
  },
  computed: {
    currentStepIndex () {
      return this.steps.indexOf(this.step)
    }
  },
  methods: {
    handleStepClick (stepLabel, stepIndex) {
      // Don't allow clicking on "Completed" step
      if (stepLabel === 'Completed') {
        return
      }

      // Don't allow clicking in preview mode
      if (this.isPreview) {
        return
      }

      // Only allow clicking on steps that are not ahead of current progress
      // This prevents jumping ahead to steps that haven't been completed
      if (stepIndex <= this.currentStepIndex || stepIndex === this.currentStepIndex + 1) {
        this.$emit('step-click', stepLabel)
      }
    }
  }
}
</script>

<style scoped>
.stepper-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-label {
  padding: 0 10px;
  font-size: 13px;
  color: #999;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.step-label.active {
  color: var(--brand-color);
  font-weight: 600;
}

.step-label.clickable {
  cursor: pointer;
}

.step-label.clickable:hover {
  color: var(--brand-color);
  opacity: 0.8;
}

.step-label.clickable:not(.active):hover {
  color: #666;
}

.divider {
  height: 2px;
  width: 40px;
  background-color: #ddd;
  transition: background-color 0.2s ease;
}

.divider.active {
  background-color: var(--brand-color);
}
</style>
