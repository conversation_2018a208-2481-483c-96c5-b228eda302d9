<template>
  <div>
  <div class="tenant">
    <div class="tenant-nav py-2 v-center space-between">
      <h1>Tenant Dashboard</h1>
    </div>
    <div class="tenant-kpi py-3 flex">
      <div class="tenant-kpi-blue p-4">
        <div class="tenant-kpi-header">Total Projects</div>
        <div class="tenant-kpi-value mt-3">{{tenantProjectList.length}}</div>
      </div>
      <div class="tenant-kpi-blue mx-4 p-4">
        <div class="tenant-kpi-header">Total Tenant</div>
        <div class="tenant-kpi-value mt-3">{{tenantList.length}}</div>
      </div>
      <div class="tenant-kpi-brand p-4">
        <img src="~@/assets/images/bg-2.png" alt="" />
        <div class="tenant-kpi-header">Activity</div>
        <div class="tenant-kpi-value mt-3">
          Here Projects and Tenant Activity will come in graph format
        </div>
      </div>
    </div>
    <div class="tab p-2 v-center">
      <div
        @click="setTab('tenantList')"
        :class="openTabName === 'tenantList' ? 'active' : null"
      >
        Companies
      </div>
      <div
        @click="setTab('userList')"
        class="mx-5"
        :class="openTabName === 'userList' ? 'active' : null"
      >
        Users
      </div>
    </div>
    <div v-if="openTabName === 'tenantList'" class="tenant-list">
      <div class="tenant-admin-bar v-center space-between my-3">
        <h3 class="weight-500 xl">Company List</h3>
        <div class="v-center">
          <div class="input-group search">
            <input
              v-model="searchKeyword"
              type="text"
               @input="performSearch(searchKeyword)"
              placeholder="Search by Name, GSTIN"
            />
          </div>
          <router-link v-if="forTenantAdmin" to="/invite-tenant">
            <button class="btn ml-3" v-if="tenantType!==2">Invite Tenant</button>
          </router-link>
        </div>
      </div>
      <div class="my-3">
        <div class="toggle-btn">
          <button :class="{ active: selectedTab === 1 || selectedTab === 4 }" @click="setSelectedTab(1, 4)" class="toggle-option">Active</button>
          <button :class="{ active: selectedTab === 2 || selectedTab === 3 }" @click="setSelectedTab(2, 3)" class="toggle-option">Inactive</button>
        </div>
      </div>
      <div>
        <copy-company-table
        :companyList="getSearchResult"
        :perPage="perPage"
        :pageNumber="pageNumber"
        :showHeader="true"
      />
      <pagination2
      v-if="getSearchResult.length"
          :length="getSearchResult.length"
          :perPage="perPage"
          :pageNumber="pageNumber"
          @selectPage="selectPage"
          class="mt-3 mx-2"
        />
      </div>
    </div>
    </div>
    <div v-if="openTabName === 'userList'" class="tenant-list">
      <div class="tenant-admin-bar v-center space-between my-3">
        <h3 class="weight-500 xl">Users List</h3>
        <div class="v-center">
          <div class="input-group search">
            <input
              v-model="searchKeyword"
              type="text"
              @input="performSearch(searchKeyword)"
              placeholder="Search by Name, Email"
            />
          </div>
          <router-link v-if="isInviteTenantLevel" to="/invite-user">
            <button class="btn ml-3">Invite User</button>
          </router-link>
            <button class="btn ml-3" @click="openAddUserDrawer" v-if="isInviteProjectLevel">Invite User</button>
        </div>
      </div>
      <div class="my-3">
    <div class="toggle-btn" v-if="!isOnProjectLevel">
      <button :class="{ active: selectedTab === 1 || selectedTab === 4 }" @click="setSelectedTab(1, 4)" class="toggle-option">Active</button>
      <button :class="{ active: selectedTab === 2 || selectedTab === 3 }" @click="setSelectedTab(2, 3)" class="toggle-option">Inactive</button>
    </div>
  </div>
      <div v-if="isTenantAdminOrCollaborator">
        <copy-user-table
          :userList="getSearchResult"
          :perPage="perPage"
          :pageNumber="pageNumber"
          :showHeader="true"
        />
        <pagination2
          :length="getSearchResult.length"
          :perPage="perPage"
          :pageNumber="pageNumber"
          @selectPage="selectPage"
          class="mt-3 mx-2"
        />
      </div>
      <div v-if="isTenantOrCollaborator">
        <copy-project-user-table
        :perPage="perPage"
        :pageNumber="pageNumber"
        :searchKeyword="searchKeyword" />
        <pagination2
        v-if="!searchKeyword"
          :length="currentProject.project_user_associations.length"
          :perPage="perPage"
          :pageNumber="pageNumber"
          @selectPage="selectPage"
          class="mt-3 mx-2"
        />
      </div>
    </div>
    <div
      v-if="currentProject.id"
      class="drawer"
      :class="addUserDrawer ? 'open' : 'close'"
    >
      <add-user-to-project
        :projectId="currentProject.id"
        @close="addUserDrawer = false"
      ></add-user-to-project>
    </div>
  </div>
</template>

<script>
import { arraySearch, arraySearchForUser } from '@/utils/array'
import copyCompanyTable from '@/components/common/copyCompanyTable.vue'
import copyUserTable from '@/components/common/copyUserTable.vue'
import { mapGetters } from 'vuex'
import AddUserToProject from '../../components/manage/addUserToProject.vue'
import copyProjectUserTable from '@/components/common/copyProjectUserTable.vue'
import pagination2 from '@/components/common/pagination2.vue'
import { generateS3DownloadingUrl } from '@/api'

export default {
  name: 'TenantAdmin',
  components: { AddUserToProject, copyCompanyTable, copyUserTable, pagination2, copyProjectUserTable },
  data () {
    return {
      collabTenants: [],
      openTabName: 'tenantList',
      searchKeyword: '',
      pageNumber: 1,
      perPage: 10,
      loading: true,
      addUserDrawer: false,
      selectedTab: 1,
      secondTab: 4
    }
  },
  mounted () {
    if (this.$route.query?.list === 'userList') {
      this.setTab('userList')
    }
    this.fetchTenantsBasedOnTab()
    this.loadCompanyLogo()
  },
  methods: {
    setSelectedTab (firstNum, secondNum) {
      this.selectedTab = firstNum
      this.secondTab = secondNum
      this.pageNumber = 1
    },
    fetchTenantsBasedOnTab () {
      this.collabTenants = JSON.parse(JSON.stringify(this.childTenantsList.map((item) => ({
        id: item.target_tenant.id,
        company_name: item.target_tenant.company_name,
        company_phone: item.target_tenant.company_phone,
        company_email: item.target_tenant.company_email,
        status: item.target_tenant.status,
        associationStatus: item.status,
        company_logo_blob_key: item.target_tenant.company_logo_blob_key,
        company_url: item.target_tenant.company_url,
        industry_vertical_value: item.target_tenant.industry_vertical_value,
        GSTIN: item.target_tenant.GSTIN
      })))).filter((item) => item.associationStatus === this.selectedTab || item.associationStatus === this.secondTab)
    },
    selectPage (pageNumber) {
      this.pageNumber = pageNumber
    },
    setTab (tab) {
      this.pageNumber = 1
      this.openTabName = tab
      this.searchKeyword = ''
      this.$router.push(`?list=${tab}`)
    },
    openAddUserDrawer () {
      this.addUserDrawer = true
    },
    loadCompanyLogo () {
      const S3Objects = []
      this.collabTenants.forEach((item) => {
        if (item.company_logo_blob_key !== null) {
          S3Objects.push({
            fileName: item.company_name,
            S3Key: item.company_logo_blob_key
          })
        }
      })
      generateS3DownloadingUrl({
        S3Objects: S3Objects
      }).then((res) => {
        const logoMap = {}
        for (const thumbnail of res.url) {
          logoMap[thumbnail.S3Key] = thumbnail.url
        }
        this.collabTenants.forEach((company) => {
          const blobkey = company.company_logo_blob_key
          const url = logoMap[blobkey]
          if (url) {
            company.company_logo_blob_key = url
          }
        })
      }).catch((err) => {
        console.log(err)
      })
    },
    performSearch (keyword) {
      this.searchKeyword = keyword // Update search keyword
      this.pageNumber = 1 // Reset page number when searching
    }
  },
  computed: {
    ...mapGetters(['tenantList', 'tenantUsersList', 'tenantProjectList', 'isOnProjectLevel', 'childTenantsList', 'tenantType']),
    ...mapGetters(['user', 'getUserById', 'currentProject', 'collaborator']),
    getSearchResult () {
      if (this.openTabName === 'tenantList') {
        return arraySearch(
          this.collabTenants,
          this.searchKeyword,
          { fieldsToSearch: ['company_name', 'GSTIN'] }
        )
      } else {
        return arraySearchForUser(
          this.tenantUsersList.filter(
            user => user.status === this.selectedTab || user.status === this.secondTab
          ),
          this.searchKeyword,
          { fieldsToSearch: ['first_name', 'last_name', 'email'] }
        )
      }
    },
    forTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN' && this.isOnProjectLevel === false && !this.collaborator
      )
    },
    isTenantAdminOrCollaborator () {
      return (
        (this.user.tenantLevelRole === 'ADMIN' ||
          this.user.tenantLevelRole === 'COLLABORATOR' || this.user.tenantLevelRole === 'EDITOR' || this.user.tenantLevelRole === 'VIEWER') && this.isOnProjectLevel === false
      )
    },
    isInviteTenantLevel () {
      return (
        (this.user.tenantLevelRole === 'ADMIN') && this.isOnProjectLevel === false && !this.collaborator
      )
    },
    isTenantOrCollaborator () {
      return (
        (this.user.projectLevelRole === 'ADMIN' ||
          this.user.projectLevelRole === 'COLLABORATOR' || this.user.projectLevelRole === 'EDITOR' || this.user.projectLevelRole === 'VIEWER')
      )
    },
    isInviteProjectLevel () {
      return (
        (this.user.projectLevelRole === 'ADMIN') && !this.collaborator
      )
    }
  },
  watch: {
    selectedTab () {
      // Dispatch the setup action with the new selectedTab
      this.fetchTenantsBasedOnTab()
    },
    childTenantsList: {
      handler (newVal) {
        this.collabTenants = JSON.parse(JSON.stringify(newVal.map((item) => ({
          id: item.target_tenant.id,
          company_name: item.target_tenant.company_name,
          company_phone: item.target_tenant.company_phone,
          company_email: item.target_tenant.company_email,
          status: item.target_tenant.status,
          associationStatus: item.status,
          company_logo_blob_key: item.target_tenant.company_logo_blob_key,
          company_url: item.target_tenant.company_url,
          industry_vertical_value: item.target_tenant.industry_vertical_value,
          GSTIN: item.target_tenant.GSTIN
        }))))
        this.loadCompanyLogo()
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped >
.tenant {
  height: 100%;
  &-nav {
    border-bottom: var(--border);
    h1 {
      font-weight: 500;
    }
  }
  &-kpi {
    align-items: stretch;
    color: var(--white);
    font-weight: 600;
    border-bottom: var(--border);
    &-blue {
      height: 180px;
      width: 225px;
      background-image: url("~@/assets/images/bg-1.png");
      border-radius: 6px;
    }
    &-brand {
      position: relative;
      flex-grow: 1;
      border-radius: 6px;
      background: linear-gradient(
        317.26deg,
        #dfa600 -26.68%,
        #ffc467 42.21%,
        #e8892a 90.37%
      );
      & img {
        position: absolute;
        bottom: 10px;
        right: 10px;
        height: 160px;
      }
      & .tenant-kpi-value {
        font-size: 20px;
      }
    }
    &-header {
      font-size: 24px;
    }
    &-value {
      font-size: 64px;
    }
  }
  .tab {
    border-bottom: var(--border);
    & > div {
      cursor: pointer;
    }
    .active {
      font-weight: 500;
      border-bottom: 2px solid var(--brand-color);
    }
  }
}
.toggle-btn {
  height: 2em;
  display: inline-flex;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #e0e0e0;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.toggle-option {
  flex: 1;
  padding: 6px 14px;
  border: none;
  background-color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.toggle-option.active {
  background-color: white;
  color: black;
  border: 2px solid #bbb;
  transition: all 0.15s ease-in-out;
  border-radius: 6px;
  z-index: 2;
}

.toggle-option:first-child.active {
  box-shadow: 4px 0 6px -2px rgba(0, 0, 0, 0.4);
}

.toggle-option:last-child.active {
  box-shadow: -4px 0 6px -2px rgba(0, 0, 0, 0.4);
}
</style>
