<template>
  <div class="add-user">
    <div class="add-user__header">
      <h2 class="add-user__title">Add User</h2>
    </div>
    <div class="select-tenant">
      <span class="select-tenant__header">Select Tenant:</span>
      <select
          class="pointer mx-4 tenant-dropdown"
          v-model="selectedTenant"
          @click="selectedUserId = null"
        >
          <option
            v-for="tenant in filterCompany"
            :key="tenant.id"
            :value="tenant.id"
          >
            {{ tenant.company_name }}
          </option>
          <optgroup label="Child Tenants" v-if="childTenantsList.length">
            <option v-for="childTenant in filterChildTenant" :key="childTenant.id"
            :value="childTenant.target_tenant.id">
              {{ childTenant.target_tenant.company_name }}
            </option>
          </optgroup>
        </select>
    </div>
    <div class="add-user__body" ref="addUserBody">
      <div class="add-user__search">
        <input
          type="text"
          placeholder="Search"
          @click="openPopup = true"
          v-model="searchKeyword"
        />
      </div>
      <div class="add-user__selected-user" v-if="selectedUserId">
        <div class="add-user__selected-user__avatar">
          <avatar :user="selectedUser" size="24px" />
        </div>
        <div class="add-user__selected-user__name"
        >
          {{ selectedUser.first_name }} {{ selectedUser.last_name }}
        </div>
        <div class="add-user__selected-user__action">
          <b @click="deselectUser">X</b>
        </div>
      </div>
      <div class="add-user__list" v-if="openPopup && !selectedUserId">
        <div class="add-user__list-no-result" v-if="!users.length">
          No users found
        </div>
        <div
          class="add-user__list-item"
          v-for="user in users"
          :key="user.id"
          :value="user.id"
          @click="selectUser(user.id)"
        >
          <div class="add-user__list-item__avatar">
            <avatar :user="user" size="24px" />
          </div>
          <div class="add-user__list-item__name">
            {{ user.first_name }}
            {{ user.last_name }}
          </div>
          <div class="add-user__list-item__action"></div>
        </div>
      </div>
    </div>
    <div class="add-user__role" v-if="selectedUserId">
      <div class="add-user__role__title">Role</div>
      <div class="add-user__role__list">
        <div class="add-user__role__list-item">
          <input
            type="radio"
            name="role"
            id="role1"
            value="1"
            v-model="userRole"
          />
          <label for="role1">Admin</label>
        </div>
        <div class="add-user__role__list-item">
          <input
            type="radio"
            name="role"
            id="role2"
            value="2"
            v-model="userRole"
          />
          <label for="role2">Editor</label>
        </div>
        <div class="add-user__role__list-item">
          <input
            type="radio"
            name="role"
            id="role4"
            value="4"
            v-model="userRole"
          />
          <label for="role4">Viewer</label>
        </div>
        <div class="add-user__role__list-item">
          <input
            type="radio"
            name="role"
            id="role3"
            value="3"
            v-model="userRole"
          />
          <label for="role3">Collaborator</label>
        </div>
      </div>
    </div>
    <div class="add-user__footer s">
      <button class="btn btn-black-outline mx-2" @click="closeModal">
        Cancel
      </button>
      <button :disabled="!selectedTenant" @click="addUserToProject" class="btn">Add User</button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import avatar from '../common/avatar.vue'
import { AddUserToProjectData, RestoreUserFormProjectData, tenantUsers, InviteUser } from '@/api'
import { alert, success } from '@/plugins/notification'
export default {
  components: { avatar },
  props: {
    projectId: {
      type: [String, Number],
      required: true
    }
  },
  data () {
    return {
      openPopup: false,
      selectedTenant: null,
      userRole: 4,
      userList: [],
      selectedUserId: null,
      searchKeyword: ''
    }
  },
  computed: {
    ...mapGetters(['currentProject', 'tenantList', 'childTenantsList', 'tenantUsersList', 'openTenantId']),
    users () {
      if (!Array.isArray(this.userList)) {
        return []
      }
      let usersList
      if (this.searchKeyword) {
        usersList = this.userList.map((item) => {
          return {
            id: item.associated_user.id,
            first_name: item.associated_user.first_name,
            last_name: item.associated_user.last_name
          }
        }).filter((item) => `${item.first_name} ${item.last_name}`.replace(' ', '').toLowerCase().includes(this.searchKeyword.toLowerCase().replace(' ', '')))
      } else {
        usersList = this.userList.map((item) => {
          return {
            id: item.associated_user.id,
            first_name: item.associated_user.first_name,
            last_name: item.associated_user.last_name
          }
        })
      }
      usersList = usersList.filter((item) => !this.currentProject.project_user_associations.find((user) => user.user_id === item.id && user.status !== 3))
      return usersList
    },
    filterCompany () {
      return this.tenantList.filter((tenant) => tenant.status === 1 && tenant.id === this.openTenantId)
    },
    filterChildTenant () {
      return this.childTenantsList.filter((tenant) => ((tenant.status === 1 || tenant.status === 4) && tenant.target_tenant.status === 1))
    },
    selectedUser () {
      return this.users.find(user => user.id === this.selectedUserId)
    }
  },
  methods: {
    updateselectedTenant (user) {
      this.selectedTenant = user.id
      this.openPopup = false
    },
    closeModal () {
      this.searchKeyword = ''
      this.selectedTenant = null
      this.selectedUserId = null
      this.$emit('close')
    },
    addUserToProject () {
      if (!this.selectedUserId) {
        return
      }
      if (!this.userRole) {
        return
      }
      const user = this.currentProject.project_user_associations.find(
        (item) => item.user_id === this.selectedUserId
      )
      if (user?.status === 1) {
        alert('User already added')
      } else if (user?.status === 4) {
        alert('User already invited')
      } else if (user?.status === 3) {
        RestoreUserFormProjectData({
          user_id: this.selectedUserId,
          role_id: +this.userRole
        }).then((res) => {
          success('User added successfully')
          this.userRole = null
          this.selectedTenant = null
          this.selectedUserId = null
          this.$emit('close')
          this.$store.dispatch('getCurrentProject')
        }).catch(() => {
          alert('Something went wrong')
        })
      } else {
        const foundUser = this.tenantUsersList.find(tenantUser => {
          return tenantUser.associated_user.id === this.selectedUserId
        })
        if (foundUser) {
          AddUserToProjectData({
            user_id: this.selectedUserId,
            role_id: +this.userRole
          }).then((res) => {
            this.selectedTenant = null
            this.selectedUserId = null
            this.searchKeyword = ''
            success('User added successfully')
            this.userRole = null
            this.$emit('close')
            this.$store.dispatch('getCurrentProject')
          }).catch(() => {
            alert('Something went wrong')
          })
        } else {
          for (const newUserList of this.userList) {
            const body = {
              firstName: newUserList.associated_user.first_name,
              lastName: newUserList.associated_user.last_name,
              email: newUserList.associated_user.email,
              role: this.userRole
            }
            InviteUser(body).then(() => AddUserToProjectData({
              user_id: this.selectedUserId,
              role_id: +this.userRole
            }).then((res) => {
              this.selectedTenant = null
              this.selectedUserId = null
              success('User added successfully')
              this.userRole = null
              this.$emit('close')
              this.$store.dispatch('getCurrentProject')
            }).catch(() => {
              alert('Something went wrong')
            }))
          }
        }
      }
    },
    handleOutsideClick (e) {
      if (!this?.$refs?.addUserBody?.contains(e.target)) {
        this.openPopup = false
      }
    },
    allUsers () {
      if (this.selectedTenant != null) {
        tenantUsers(this.selectedTenant).then((res) => {
          this.userList = res.tenant_user_association
        })
      }
    },
    selectUser (userId) {
      this.selectedUserId = userId
    },
    deselectUser () {
      this.selectedUserId = null
    }
  },
  watch: {
    selectedTenant: 'allUsers'
  },
  mounted () {
    document.addEventListener('click', this.handleOutsideClick)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleOutsideClick)
  }
}
</script>

<style lang="scss" scoped >
.add-user {
  width: 300px;
  &__header {
    padding: 1rem;
    border-bottom: 1px solid #e8e8e8;
  }
  &__title {
    font-size: 1.5rem;
    font-weight: 500;
  }
  &__body {
    padding: 1rem;
    position: relative;
  }
  &__selected-user {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e8e8e8;
    &__avatar {
      margin-right: 1rem;
    }
    &__name {
      flex: 1;
    }
    &__action {
      cursor: pointer;
      b {
        font-size: 1rem;
      }
    }
  }
  &__search {
    margin-bottom: 1rem;
    input {
      width: 100%;
      padding: 0.5rem;
      border: 1px solid #e8e8e8;
      border-radius: 0.25rem;
    }
  }
  &__list {
    max-height: 20rem;
    overflow-y: auto;
    position: absolute;
    background: white;
    left: 16px;
    right: 16px;
    z-index: 1;
    top: 45px;
    padding: 10px;
    box-shadow: 2px 2px 4px rgb(0 0 0 / 20%);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    &-no-result {
      padding: 1rem;
      text-align: center;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      &:hover {
        background: #f8f8f8;
      }
      &__avatar {
        margin-right: 1rem;
      }
      &__name {
        flex: 1;
      }
      &__action {
        &__btn {
          padding: 0.25rem;
          border: 1px solid #e8e8e8;
          border-radius: 0.25rem;
          background: #fff;
          cursor: pointer;
          i {
            font-size: 1.25rem;
          }
        }
      }
    }
  }
  &__role {
    padding: 1rem;
    padding-top: 0;
    &__title {
      font-size: 1.25rem;
      font-weight: 500;
    }
    &__list {
      &-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e8e8e8;
        cursor: pointer;
        &:hover {
          background: #f8f8f8;
        }
        &__avatar {
          margin-right: 1rem;
        }
        &__name {
          flex: 1;
        }
        &__action {
          &__btn {
            padding: 0.25rem;
            border: 1px solid #e8e8e8;
            border-radius: 0.25rem;
            background: #fff;
            cursor: pointer;
            i {
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
  &__footer {
    padding: 1rem;
    text-align: right;
    &__btn {
      padding: 0.5rem 1rem;
      border: 1px solid #e8e8e8;
      border-radius: 0.25rem;
      background: #fff;
      cursor: pointer;
    }
  }
}
.select-tenant__header{
  padding: 25px 18px 75px;
}
.tenant-dropdown {
  width: 268px;
  height: 30px;
}
</style>
