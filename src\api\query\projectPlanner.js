import { GQL } from '../graphQl'

export const GetTasksAndLinks = () => GQL`query GetCoreTasks( $conditions: core_tasks_bool_exp, $skipLinksFetching: Boolean!) {
  task_links @skip(if: $skipLinksFetching) {
    type: link_type_id
    source: src_task_id
    target: target_task_id
    lag
  }
  core_tasks(where: $conditions, order_by: {created_at: asc}) {
    id
    text: name
    slack
    description
    start_date: planned_start_date
    planned_start_date
    planned_end_date
    projected_end_date
    projected_start_date
    progress
    actual_start_date
    actual_end_date
    parent: parent_task_id
    cost
    spi
    critical_progress
    order_index
    task_material_associations : associated_links_by_source_id(where: {target_feature_id: {_eq: 2}}) {
      id
      target_bom {
        name
        id
        bom_versions(where:{active: {_eq: true}}){
          id
          total_cost
        }
      }
      metadata
    }
    task_assignees {
      user_id
      assignee {
        first_name
        last_name
        id
      }
    }
    duration
    is_critical
    type
    level
    status
    target_task_links {
      src_task {
        id
        name
        planned_end_date
        planned_start_date
        projected_end_date
        projected_start_date
        progress
        is_critical
        task_assignees {
          user_id
          assignee {
            first_name
            last_name
            id
          }
        }
      }
    }
    src_task_links {
      target_task {
        id
        name 
        planned_end_date
        planned_start_date
        projected_end_date
        projected_start_date
        progress
        is_critical
        task_assignees {
          user_id
          assignee {
            first_name
            last_name
            id
          }
        }
      }
    }
    tag_tasks {
      tag {
        id
        name
        parent_id
      }
    }
    core_comments{
      comment
    }
    parent_core_task{
      id
      text: name
    }
  }
}`

export const CreateTask = () => GQL`mutation createProjectTask1 ($data: core_tasks_insert_input!) {
  insert_core_tasks_one(object:$data) {
    id
  }
}`

export const AddTask = () => GQL`mutation createProjectTask ($data: core_tasks_insert_input!) {
  insert_core_tasks_one(object:$data) {
    id
  }
}`

export const GetAllTaskBom = () => GQL`query getAllTaskBoms ($taskId: uuid) {
  task_material_association : linking_service(where: {source_id: {_eq: $taskId}, target_feature_id: {_eq: 2}}){
    id
    metadata
    target_bom {
      id
      name
      bom_versions(where: {active: {_eq: true}}) {
        id
      }
    }
  }
}`

export const addBomToTask = () => GQL`mutation AddBomToTask($data: [linking_service_create_input]) {
  create_linking_service(data: $data) {
    message
  }
}`

export const UpdateBomTaskAssociation = () => GQL`mutation UpdateTaskMaterialAssociation($metadata: jsonb, $id: uuid!) {
  update_linking_service_by_pk(_set: {metadata: $metadata}, pk_columns: {id: $id}) {
    id
  }
}`

export const DeleteBomFromTask = () => GQL`mutation DeleteTaskMaterialAssociation($id: uuid!) {
  delete_linking_service_by_pk (id : $id) {
    id
  }
}`

export const UpdateTaskQuery = () => GQL`mutation updateTask($id: uuid!, $data: core_tasks_set_input) {
  update_core_tasks_by_pk(_set: $data, pk_columns: {id: $id}) {
    id
    parent_task_id
  }
}`

export const updateTasksQuery = () => GQL`mutation updateTasks ($taskUpdates: [core_tasks_updates!]!) {
  update_core_tasks_many(updates: $taskUpdates) {
    affected_rows
  }
}`

export const addNewAssigneesMutation = () => GQL`mutation addNewAssigneesMutation ($assignees: [task_assignee_insert_input!]!) {
  insert_task_assignee(objects: $assignees){
    affected_rows
  }
}`

export const deleteAssigneesMutation = () => GQL`mutation deleteAssigneesMutation ($conditions: task_assignee_bool_exp!){
  delete_task_assignee(where: $conditions) {
    affected_rows
  }
}
`

export const InsertTaskLinks = () => GQL`mutation insertTaskLink($src_task_id: uuid!, $target_task_id: uuid!, $link_type_id: Int!, $lag: Int) {
  insert_task_links_one(
    object: {src_task_id: $src_task_id, target_task_id: $target_task_id, link_type_id: $link_type_id, lag: $lag}
  ) {
    updated_at
  }
}`

export const deleteTaskLinks = () => GQL`mutation deleteTaskLinks($src_task_id: uuid!, $target_task_id: uuid!) {
  delete_task_links_by_pk(
    src_task_id: $src_task_id
    target_task_id: $target_task_id
    ) {
    updated_at
  }
}`

export const getTaskStatuses = () => GQL`query GetTaskStatuses ($conditions: custom_list_values_bool_exp){
  custom_list_values(where: $conditions, order_by: { id: asc }) {
    name
    id
  }
}`

export const ChangeAssigneeQuery = () => GQL`mutation deleteAssignee($old_user_id: uuid, $new_user_id: uuid, $status: [Int!]!, $tenant_id: uuid) {
  delete_task_assignee(
    where: {user_id: {_eq: $new_user_id}, core_task: {status:{_in: $status} task_assignees: {user_id: {_eq: $old_user_id}}}}
  ) {
    affected_rows
  }
  update_task_assignee(
    where: {user_id: {_eq: $old_user_id}, core_task: {status: {_in: $status}}}
    _set: {user_id: $new_user_id, target_tenant_id: $tenant_id}
  ) {
    affected_rows
  }
}`

export const getAllTaskUsersQuery = () => GQL`query getAllTaskUsers($status: [Int!]!) {
   task_assignee(where: {core_task: {status: {_in: $status}}}, distinct_on: user_id) {
    assignee {
      first_name
      last_name
      status
    }
    user_id
  }
}
`

export const getProjectImports = () => GQL`{
  project_imports(where: {type: {_eq: 1}}) {
    id
    name
    blob_key
  }
}`

export const saveBaseline = () => GQL`mutation saveBaseline($name:String!){
    save_baseline(name:$name){
      message
    }
  }
`
export const updateProjectedEndDateMutation = () => GQL`mutation updateProjectedEndDateMutation($projected_end_date: timestamptz, $id: uuid!, $progress: Int) {
  update_core_tasks_by_pk(
    pk_columns: {id: $id}
    _set: {projected_end_date: $projected_end_date, progress: $progress}
  ) {
    id
  }
}`

export const updateTaskLinkMutation = () => GQL`mutation updateTaskLinkMutation ($data: task_links_set_input, $target_task_id: uuid!, $src_task_id: uuid! ){
  update_task_links_by_pk(_set: $data, pk_columns: {target_task_id: $target_task_id, src_task_id: $src_task_id}) {
    target_task_id
  }
}`

export const addDocumentToTask = () => GQL`mutation AddTaskDocumentAssociation($data: [task_document_association_insert_input!]!) {
  insert_task_document_association(objects: $data) {
    affected_rows
  }
}`

export const GetAllTaskDocs = () => GQL`query GetAllTaskDocs($taskId:uuid!){
  task_document_association(where:{task_id:{_eq:$taskId}}){
      task_id
      document_id
      core_document {
        doc_name
        id
        doc_type
        doc_ext
        doc_size
        blob_key
        version_of
        created_by
        created_on
        thumbnail_blob_key
        inherited_from_doc_id
         inherited_from_document {
          blob_key
          doc_name
          doc_type
           id
           thumbnail_blob_key
        }
      }
   }
}`

export const removeTaskDocs = () => GQL`mutation removeTaskDocs($document_id:uuid!, $task_id: uuid!) {
  delete_task_document_association(where:{document_id :{_eq:$document_id},task_id: {_eq:$task_id}}) {
    affected_rows
  }
}`

export const deleteTasks = () => GQL`mutation deleteTasks ($taskIds: [uuid!]) {
  delete_core_tasks(where: { id: {_in: $taskIds} }) {
    affected_rows
  }
}`

export const checkTimesheetEntry = () => GQL`query checkTimesheetEntry($taskId: uuid!) {
  user_timesheet(where: {task_id: {_eq: $taskId}}, order_by: {entry_date: desc}) {
    id
    duration
    entry_date
    time_remaining
  }
}`

export const checkIfTaskIsParent = () => GQL`query checkIfTaskIsParent ($taskId: uuid!){
  core_tasks (where: {parent_task_id: {_eq: $taskId}}){
    id
    name
  }
}`

export const checkTimeRemainigExistQuery = () => GQL`query checkTimeRemainigQuery($task_id: uuid, $user_id: uuid, $entry_date: timestamptz) {
  user_timesheet(
    where: {task_id: {_eq: $task_id}, user_id: {_eq: $user_id}, entry_date: {_eq: $entry_date}}
  ) {
    id
    time_remaining
    duration
  }
}`

export const timeSheetAggregate = () => GQL`query timeSheetAggregate($task_id: uuid, $user_id: uuid) {
  user_timesheet_aggregate(
    where: {task_id: {_eq: $task_id}, user_id: {_eq: $user_id}}
  ) {
    aggregate {
      sum {
        duration
      }
    }
  }
}
`

export const updateTaskTimeSheetDataQuery = () => GQL`mutation updateTaskTimeSheetDataQuery($duration: float8, $id: uuid!, $timeRemaining: float8 ) {
  update_user_timesheet_by_pk(
    pk_columns: {id: $id}
    _set: {duration: $duration, time_remaining: $timeRemaining}
  ) {
    id
    user_id
  }
}
`
export const getTaskDataByIdQuery = () => GQL`query getTaskDataByIdQuery($isCollaborator: Boolean!, $taskId: uuid!) {
  core_tasks_by_pk(id: $taskId) {
    id
    text: name
    slack
    description
    start_date: planned_start_date
    planned_start_date
    planned_end_date
    projected_end_date
    projected_start_date
    progress
    actual_start_date
    actual_end_date
    task_material_associations: associated_links_by_source_id(where: {target_feature_id: {_eq: 2}}) {
      target_bom {
        name
        id
          bom_versions(where: {active: {_eq: true}}) {
          id
          total_cost
        }
      }
      metadata
    }
    parent: parent_task_id @skip(if: $isCollaborator)
    cost
    spi
    critical_progress
    order_index
    task_assignees {
      user_id
      assignee {
        first_name




        last_name
        id
      }
    }
    duration
    is_critical
    type
    level
    status
    target_task_links {
      src_task {
        id
        name
        planned_end_date
        planned_start_date
        projected_end_date
        projected_start_date
        progress
        is_critical
        task_assignees {
          user_id
          assignee {
            first_name
            last_name
            id
          }
        }
      }
    }
    tag_tasks @skip(if: $isCollaborator) {
      tag {
        id
        name
        parent_id
      }
    }
    parent_core_task {
      id
      text: name
    }
    core_project {
      id
    }
  }
}


`

export const getAllTasksQuery = () => GQL`query getAllTasksQuery($filters: core_tasks_bool_exp){
  core_tasks(where: $filters) {
    id
    name
    planned_start_date
    planned_end_date
    is_critical
    projected_start_date
    projected_end_date
    slack
    description
    duration
    progress
    actual_start_date
    actual_end_date
    critical_progress
    cost
    spi
  }
}`
