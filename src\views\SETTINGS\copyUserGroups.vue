<template>
    <div class="user-groups fh">
      <div class="user-groups-bar v-center space-between px-5">
        <h1 class="weight-500 xl">
          User Groups
        </h1>
        <div class="v-center">
          <div class="input-group search m mx-3">
            <input
              v-model="searchKeyword"
              type="text"
              @input="updateOnSearch"
              placeholder="Search by name"
            />
          </div>
          <router-link to="user-groups/create" v-if="user.tenantLevelRole === 'ADMIN'">
              <button v-if="isTenantAdmin" class="btn btn-black">Create User Group</button>
            </router-link>
        </div>
      </div>
      <div class="user-groups-container">
        <div class="fh center" v-if="loading">
          <loading-circle />
          </div>
        <div v-else-if="totalCount === 0" class="user-groups-no-form fh center">
          No User Groups
        </div>
        <div class="copy-dtx-table" v-else>
        <table v-if="userGroups.length > 0">
          <thead>
            <tr class="m">
              <th>No.</th>
              <th>Name</th>
              <th>Description</th>
              <th>Members</th>
              <th>Created By</th>
              <th v-if="isTenantAdmin">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in userGroups" :key="row.id" @click="gotoUserGroupDetail(row.id)" class="pointer">
                <td>{{ (pageNumber - 1) * perPage + index + 1 + ")" }}</td>
                <td class="elipsis-text user-group-name" v-overflow-tooltip="row.name">{{ row.name }}</td>
                <td class="elipsis-text description" v-overflow-tooltip="row.description">{{ row.description }}</td>
                <td>
                  <div class="avatar-container flex">
                    <avatar v-for="user in row.formattedUserGroupMembers" :key="user.id" :user="user.core_user"
                      :title="getUserTooltip(user.core_user)" style="margin-right: -13.5px"/>
                    <avatar style="margin-right: -13.5px" v-if="row.exceededCount" :exceeded-count="row.exceededCount" :title="row.exceededCount + ' ' + 'more'"/>
                  </div>
                </td>
                <td>
                  {{
                     row?.created_by_user?.first_name +
                      " " +
                      row?.created_by_user?.last_name
                  }}
                </td>
                <td v-if="isTenantAdmin" class="action-column" >
                   <img @click.stop="gotoEditPage(row.id)" src="~@/assets/images/pencil.svg"
                    class="mr-2 pointer" v-tooltip="'Edit User Group'" />
                   <img @click.stop="deleteUserGroup(index)" src="~@/assets/images/trash-2.svg"
                    class="pointer" v-tooltip="'Delete User Group'" />
                    </td>
            </tr>
          </tbody>
        </table>
        <div class="pagination-footer space-between">
            <pagination2
       v-if="totalCount>10"
          :length="totalCount" :pageNumber="pageNumber" :perPage="perPage"
            @selectPage="selectPage"
          class="mt-3 mx-2"
        />
          <span class="pagination-footer-total mt-4"  v-if="userGroups.length > 0">Total User Groups : &nbsp; <b> {{ totalCount }}</b></span>
        </div>
      </div>
      </div>
    </div>
  </template>

<script>
import { mapGetters } from 'vuex'
import { GetUserGroups, DeleteUserGroup, CheckIfUserGroupIsAttachedToWorkflow } from '@/api'
import { timeStampToDateTime } from '@/filters/dateFilter.js'
import Pagination2 from '@/components/common/pagination2.vue'
import LoadingCircle from '@/components/common/loadingCircle.vue'
import Avatar from '@/components/common/avatar.vue'
import { alert, success } from '@/plugins/notification'
import ConfirmationDialog from '@/plugins/confirmationDialog'
import { debounce } from '@/utils/debounce'

export default {
  components: { Pagination2, LoadingCircle, Avatar },
  name: 'user-groups',
  filters: {
    timeStampToDateTime
  },
  data: () => ({
    userGroups: [],
    loading: false,
    searchKeyword: '',
    pageNumber: 1,
    perPage: 8,
    totalCount: 0,
    updateOnSearch: null
  }),
  computed: {
    ...mapGetters(['user', 'getUserById', 'isOnProjectLevel']),
    isTenantAdmin () {
      return (
        this.user.tenantLevelRole === 'ADMIN'
      )
    }
  },
  methods: {
    updateQuery (search, page) {
      this.$router.push({
        query: {
          ...this.$route.query,
          search: search || undefined,
          page: page || 1
        }
      })
    },
    gotoEditPage (id) {
      this.$router.push(`/settings/user-groups/edit/${id}`)
    },
    gotoUserGroupDetail (id) {
      this.$router.push(`/settings/user-groups/detail/${id}`)
    },
    selectPage (page) {
      this.pageNumber = page
      this.updateQuery(this.searchKeyword, page)
      this.getUserGroups()
    },
    getUserTooltip (user) {
      return `${user.first_name} ${user.last_name}`
    },
    deleteUserGroup (index) {
      const id = this.userGroups[index].id
      CheckIfUserGroupIsAttachedToWorkflow(id).then((res) => {
        if (res.workflow_stages.length > 0) {
          alert('This user group is attached to a workflow and cannot be deleted.')
          return
        }
        ConfirmationDialog(`Are you sure you want to delete ${this.userGroups[index].name}?`, (res) => {
          if (res) {
            DeleteUserGroup(id)
              .then((res) => {
                success('Successfully deleted user group')
                this.getUserGroups()
              })
              .catch(() => {
                alert('Failed to delete user group')
              })
          }
        })
      }).catch(() => {
        alert('Unable to delete user group')
      })
    },
    async getUserGroups () {
      this.loading = true
      try {
        const res = await GetUserGroups((this.pageNumber - 1) * this.perPage, this.perPage, this.searchKeyword)
        this.userGroups = res.core_user_group
        for (const user of this.userGroups) {
          const userMemberscount = user.core_user_group_members.length
          if (userMemberscount <= 3) {
            user.formattedUserGroupMembers = user.core_user_group_members
          } else {
            user.formattedUserGroupMembers = user.core_user_group_members.slice(0, 3)
            user.exceededCount = userMemberscount - 3
          }
        }
        this.totalCount = res.core_user_group_aggregate.aggregate.count
      } catch (err) {
        alert('Unable to fetch user groups')
      } finally {
        this.loading = false
      }
    },
    closePreview () {
      this.selectedTemplate = {
        id: 0,
        open: false,
        loading: false,
        data: [],
        name: '',
        type: '',
        version: 0
      }
    },
    getFullName (user) {
      return `${user.first_name} ${user.last_name}`
    }
  },
  created () {
    this.pageNumber = parseInt(this.$route.query.page) || 1
    this.searchKeyword = this.$route.query.search || ''
    this.updateOnSearch = debounce(() => {
      this.pageNumber = 1
      this.updateQuery(this.searchKeyword, this.pageNumber)
      this.getUserGroups()
    }, 500)
    this.getUserGroups()
  }
}
</script>

  <style lang="scss" scoped >
  .user-groups {
    .user-group-name {
       max-width: 80px;
    }
    .description {
        max-width: 60px;
    }
    &-bar {
      height: 60px;
      margin-bottom: 0;
      background: var(--bg-color);
      border-bottom: var(--border);
    }
    &-container {
      height: calc(100% - 80px);
    }
    &-no-form {
      font-size: 20px;
      color: var(--text-color);
    }
    &-group {
      overflow: auto;
      position: relative;
      & > div {
        & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          & > div {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        }
      }
      &-header {
        background-color: var(--brand-color);
        position: sticky;
        top: 0;
        &-item {
          padding: 4px 10px;
          font-size: 14px;
          font-weight: 600;
          color: var(--text-color);
        }
      }
      &-row {
        display: grid;
        grid-template-columns: 2fr 3fr 1fr 1fr 80px;
      }
      &-item {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        & > div {
          padding: 4px 10px;
          font-size: 14px;
          color: var(--text-color);
        }
      }
    }
    &-preview {
      &-header {
        padding: 5px 10px;
        background: var(--brand-color);
        & > h3 {
          font-size: 14px;
          font-weight: 600;
          color: var(--text-color);
        }
        & select {
          font-size: 10px;
          background-color: var(--brand-light-color);
          line-height: 1;
          border: 1px solid rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          padding: 2px 6px;
        }
      }
      &-body {
        height: calc(100% - 40px);
        padding: 10px;
        background: var(--bg-color);
        overflow-y: auto;
      }
    }
  }
  </style>
