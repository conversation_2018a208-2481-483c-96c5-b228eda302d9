// src/utils/formFieldIcons.js
const TextIcon = require('@/assets/images/icons/text-icon.svg')
const LongTextIcon = require('@/assets/images/icons/long-text.svg')
const NumberIcon = require('@/assets/images/icons/number-icon.svg')
const DateIcon = require('@/assets/images/icons/date-icon.svg')
const TimeIcon = require('@/assets/images/icons/time-icon.svg')
const SwitchIcon = require('@/assets/images/icons/switch-icon.svg')
const ListIcon = require('@/assets/images/icons/list-icon.svg')
const AttachmentIcon = require('@/assets/images/icons/attachment-icon.svg')
const UserIcon = require('@/assets/images/icons/single-user.svg')
const MultiUserIcon = require('@/assets/images/icons/multiple-users.svg')
const MaterialIcon = require('@/assets/images/material-master-icon.png')
const LocationIcon = require('@/assets/images/icons/location-icon.svg')
const ProductCodeIcon = require('@/assets/images/icons/productcode-icon.svg')
const TagIcon = require('@/assets/images/icons/tag-icon.svg')
const BomIcon = require('@/assets/images/bom-icon.png')
const DocumentIcon = require('@/assets/images/documents-icon.png')

export const iconMap = {
  TEXT: TextIcon,
  LONG_TEXT: LongTextIcon,
  NUMBER: NumberIcon,
  DATE: DateIcon,
  TIME: TimeIcon,
  BOOLEAN: SwitchIcon,
  CONFIGURATION_LIST: ListIcon,
  ATTACHMENT: AttachmentIcon,
  COMPANY: TextIcon,
  USER: UserIcon,
  MULTI_USER: MultiUserIcon,
  MATERIALS: MaterialIcon,
  LOCATION: LocationIcon,
  PRODUCT_CODE: ProductCodeIcon,
  TAGS: TagIcon,
  BOM: BomIcon,
  DOCUMENTS: DocumentIcon
}
