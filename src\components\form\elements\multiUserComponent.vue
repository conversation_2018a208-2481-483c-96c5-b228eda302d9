<template>
  <div class="flex-grow">
    <div :class="{
      'form-input': true,
      'form-input--required': data.required,
    }">
      <label v-show="data.caption">{{ data.caption }}:
        <img v-if="data.visibility" class="mx-1" width="15" src="~@/assets/images/eye.svg" alt="view" />
      </label>
    </div>
    <div class="form-location--grid-2 w-100">
      <div :class="{
        'form-input': true,
        'form-input--required': data.required,
      }">
        <div>
          <select :disabled="viewOnly" v-model="companyValue" @change="emitCompanyChange" :style="{ color: companyValue === 'none' ? 'grey' : '' }">
            <option :value="openTenantDetails" selected>
              {{ openTenantDetails?.company_name }}
            </option>

            <optgroup label="Child Tenants" v-if="childTenantsList.length">
              <option v-for="childTenant in filterChildTenant" :key="childTenant.id" :value="childTenant"
                :disabled="childTenant.status === 2 || childTenant.status === 3">
                {{ childTenant.target_tenant?.company_name }}
              </option>
            </optgroup>
          </select>
        </div>
      </div>
      <div :class="{
        'form-input': true,
        'form-input--required': data.required,
      }">
        <div class="form-user-bedge disabled" v-show="!getSelectedUser.length && viewOnly">
          <div class="no-user-selected v-center text-center p-2">No user selected</div>
        </div>
        <input v-if="!viewOnly" type="text"  v-model="searchKeyword" @focus="open = true" :style="{ height: '40px' }" placeholder="Select a User"/>
        <div class="form-input--options" v-if="open">
          <div class="center" v-if="usersList.loading">
            <loading-circle />
          </div>
          <div v-else-if="companyValue === 'none'" class="form-input--option">
            <div class="form-input--option__name">Select a Tenant First</div>
          </div>
          <div v-else-if="!usersList.loading && getUserList.length === 0" class="form-input--option">
            <div class="form-input--option__name">No users found</div>
          </div>
          <div class="form-input--option flex" v-for="user in getUserList" :key="user.id" @click="toggleUser(user)">
            <input type="checkbox" :checked="componentValue.some((item) => item.user_id === user.id)" />
            <div class="ml-2">
              <div class="form-input--option__name">
                {{ user.first_name }} {{ user.last_name }}
              </div>
              <div class="form-input--option__email">{{ user.email }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="form-user-bedge" v-show="getSelectedUser.length">
      <div v-for="item in getSelectedUser" :key="item.id" class="form-user-bedge__item">
        <div v-tooltip="getCompanyTooltip(item.id)" class="flex-column flex-grow">
          <div class="v-center ">
            <div class="form-user-bedge__name elipsis-text" v-overflow-tooltip>
              {{ item.first_name }} {{ item.last_name }}
            </div>
            <label class="ml-1">
              {{
                item?.status === 4 ? "(Invited)" : null
              }}</label>
          </div>
          <div class="form-user-bedge__email">{{ item.email }}</div>
        </div>

        <div class="form-user-bedge__action" v-if="!viewOnly" @click="removeUser(item)">
          <img src="~@/assets/images/delete-icon.svg" width="15px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { GetActiveTenantUsersList, getTenantUsersListwithInvitedUsers, GetUserListByPojIds } from '@/api'
import loadingCircle from '../../common/loadingCircle.vue'
export default {
  components: { loadingCircle },
  name: 'CompanyComponent',
  props: {
    mode: {
      type: String,
      default: 'TEMPLATE'
    },
    data: {
      type: Object,
      default: () => {}
    },
    value: {
      type: Array,
      default: () => []
    },
    viewOnly: {
      type: Boolean,
      default: false
    },
    // this prop is used to  show only active users else al users including invited
    showOnlyActiveUsers: {
      type: Boolean,
      default: false
    },
    // this is for  showing selected users in step assignee
    selectedUsers: {
      type: Array,
      default: () => ([])
    }
  },
  data () {
    return {
      searchKeyword: '',
      open: false,
      componentValue: [],
      initialValue: [],
      insert: [],
      delete: [],
      companyValue: 'none',
      userList: [],
      selectedUsersForTooltip: []
    }
  },
  computed: {
    ...mapGetters('form', ['usersList']),
    ...mapGetters('form', ['companyList']),
    ...mapGetters(['childTenantsList', 'isOnProjectLevel', 'openTenantDetails', 'openTenantId', 'openProjectId', 'isOnProjectLevel', 'parentTenantsList', 'projectIdForCollaborator']),
    filterChildTenant () {
      return this.childTenantsList.filter((item) => item.target_tenant.status === 1)
    },
    getSelectedUser () {
      return this.usersList.data.filter((user) => {
        return this.componentValue.some(item => item.user_id === user.id)
      })
    },
    getUserList () {
      if (!Array.isArray(this.userList)) {
        return []
      }
      let usersList
      if (this.searchKeyword) {
        usersList = this.userList.map((item) => {
          return {
            id: item.associated_user.id,
            first_name: item.associated_user.first_name,
            last_name: item.associated_user.last_name
          }
        }).filter((item) => `${item.first_name} ${item.last_name}`.replace(' ', '').toLowerCase().includes(this.searchKeyword.toLowerCase().replace(' ', '')))
      } else {
        usersList = this.userList.map((item) => {
          return {
            id: item.associated_user.id,
            first_name: item.associated_user.first_name,
            last_name: item.associated_user.last_name
          }
        })
      }
      return usersList
    },
    isFormCreatingPath () {
      return this.$route.path.includes('/createform/') || this.$route.path.includes('/editform/')
    }
  },
  methods: {
    getCompanyTooltip (userId) {
      const tenant = this.value.find(d => d.user_id === userId)
      const tenantUsers = this.selectedUsersForTooltip.find(d => d.user_id === userId)
      return tenant ? tenant.tenant_user_association.tenant?.company_name : tenantUsers?.company_name
    },
    allUsers () {
      if (this.companyValue != null) {
        const companyId = this.companyValue.target_tenant ? this.companyValue.target_tenant.id : this.companyValue.id

        if ((companyId === this.openTenantId) && this.isOnProjectLevel) {
          GetUserListByPojIds([this.openProjectId], [1, 4]).then((res) => {
            this.userList = res.project_user_association
          })
        } else if (this.showOnlyActiveUsers) {
          GetActiveTenantUsersList(companyId, this.isOnProjectLevel).then((res) => {
            this.userList = res.tenant_user_association
          })
        } else {
          getTenantUsersListwithInvitedUsers(companyId, this.isOnProjectLevel).then((res) => {
            this.userList = res.tenant_user_association
          })
        }
      }
    },
    emitCompanyChange () {
    },
    emitChange () {
      const value = {}
      if (this.insert.length) {
        value.insert = this.insert
      }
      if (this.delete.length) {
        value.delete = this.delete
      }
      this.$emit('customChange', {
        field_id: this.$props.data.field_id,
        value
      })
    },
    removeUser (user) {
      const index = this.componentValue.findIndex((item) => item.user_id === user.id)
      this.componentValue.splice(index, 1)
      const indexInInitialArray = this.initialValue.findIndex((item) => item === user.id)
      if (indexInInitialArray !== -1) {
        this.delete.push({ userId: this.initialValue[indexInInitialArray] })
        this.initialValue.splice(indexInInitialArray, 1)
        this.emitChange()
      } else {
        const indexInInsertArray = this.insert.findIndex((item) => item.user_id === user.id)
        if (indexInInsertArray !== -1) {
          this.insert.splice(indexInInsertArray, 1)
        }
        this.emitChange()
      }
    },
    toggleUser (user) {
      const index = this.componentValue.findIndex((item) => item.user_id === user.id)
      if (index === -1) {
        this.componentValue.push({ user_id: user.id, target_tenant_id: this.companyValue.target_tenant ? this.companyValue.target_tenant.id : this.companyValue.id })
        // checking if the user already there
        const indexInInitialArray = this.initialValue.findIndex((item) => item === user.id)
        if (indexInInitialArray === -1) {
          this.insert.push({ user_id: user.id, target_tenant_id: this.companyValue.target_tenant ? this.companyValue.target_tenant.id : this.companyValue.id })
          this.selectedUsersForTooltip.push({ user_id: user.id, target_tenant_id: this.companyValue.target_tenant ? this.companyValue.target_tenant.id : this.companyValue.id, company_name: this.companyValue.target_tenant ? this.companyValue.target_tenant?.company_name : this.companyValue?.company_name })
          this.emitChange()
        } else {
          const indexInDeleteArray = this.delete.findIndex((item) => item === user.id)
          if (indexInDeleteArray !== -1) {
            this.delete.splice(indexInDeleteArray, 1)
            this.emitChange()
          }
        }
      } else {
        this.componentValue.splice(index, 1)
        const indexInInitialArray = this.initialValue.findIndex((item) => item === user.id)
        if (indexInInitialArray !== -1) {
          this.delete.push({ userId: this.initialValue[indexInInitialArray] })
          this.emitChange()
        } else {
          const indexInInsertArray = this.insert.findIndex((item) => item.user_id === user.id)
          if (indexInInsertArray !== -1) {
            this.insert.splice(indexInInsertArray, 1)
            this.emitChange()
          }
        }
      }
    },
    closeDropdown (event) {
      if (this.$el.contains(event.target)) {
        return
      }
      this.open = false
    },
    setValue () {
      this.componentValue = this.value.map((item) => item)
    }
  },
  watch: {
    value () {
      this.setValue()
    },
    companyValue: 'allUsers'
  },
  created () {
    this.$store.dispatch('form/getUserList')
    window.addEventListener('click', this.closeDropdown)
    this.setValue()
    if (this.companyValue === 'none') {
      this.companyValue = this.openTenantDetails
    }
    this.initialValue = this.value.map((item) => item.user_id)
  },
  destroyed () {
    window.removeEventListener('click', this.closeDropdown)
  },
  mounted () {
    if (this.selectedUsers.length > 0) {
      this.componentValue = [...this.selectedUsers]
      this.insert = [...this.insert, ...this.selectedUsers]
    }
  }
}
</script>

<style lang="scss" scoped >
select{
  height: 40px;
  optgroup{
    color: var(--black)
  }
  option{
    color: var(--black)
  }
}
.form-user-bedge {
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #e5e5e5;
  font-size: 1rem;
  .form-user-bedge__item {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 5px 10px;
    margin: 5px;
    display: flex;
    align-items: center;
    width: 250px;
    .form-user-bedge__name {
      text-wrap: nowrap;
      font-size: 1em;
      font-weight: 600;
      color: #333333;
      max-width: 170px;
      & + label {
        display: flex;
        font-size: .8em;
      }
    }
    .form-user-bedge__email {
      font-size: .8em;;
      color: #333333;
    }
    .form-user-bedge__action {
      margin-left: 10px;
      cursor: pointer;
      color: red
    }
  }
  &.disabled{
    border: 1px solid rgb(195, 195, 195);
  }
  .no-user-selected {
    font-size: 12px;
    color: grey;
    display: flex;
    text-align: center;
    padding: 5px 10px;
    height:40px;
    opacity: 0.7;
  }
}
.form-location {
  font-size: 12px;
  &--label {
    margin-bottom: 0.5rem;
    font-size: 1.3em;
  }
  &--grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 1rem;
  }
}
</style>
